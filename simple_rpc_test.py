# -*- coding: utf-8 -*-
"""
简单RPC功能测试
==============

直接测试重构后的RPC功能，避免模块导入冲突。

作者：Augment Agent
版本：1.0.0
"""

import multiprocessing
import time
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 直接导入模块，避免__init__.py的Logger冲突
import global_tools.utils.enhanced_process_rpc.proxy_manager as proxy_module


class SimpleTestService:
    """简单测试服务类"""
    
    def __init__(self):
        self.counter = 0
        self.message = "初始消息"
        self.data = {"status": "初始状态"}
        
    def increment(self):
        """递增计数器"""
        self.counter += 1
        print(f"[主进程] 计数器递增到: {self.counter}")
        return self.counter
        
    def set_message(self, msg):
        """设置消息"""
        old_msg = self.message
        self.message = msg
        print(f"[主进程] 消息从 '{old_msg}' 更新为 '{msg}'")
        return old_msg
        
    def get_status(self):
        """获取状态"""
        return {
            'counter': self.counter,
            'message': self.message,
            'data': self.data.copy()
        }
        
    def update_data(self, key, value):
        """更新数据"""
        self.data[key] = value
        print(f"[主进程] 数据更新: {key} = {value}")


def child_process():
    """子进程函数"""
    print("\n[子进程] 开始执行")
    
    try:
        # 创建RPC管理器
        manager = proxy_module.SimpleRPCManager()
        
        # 创建代理对象
        service_proxy = proxy_module.ProcessProxy(manager, 'test_service')
        print("[子进程] 代理对象已创建")
        
        # 测试方法调用
        print("[子进程] 测试1: 递增计数器")
        result1 = service_proxy.increment()
        print(f"[子进程] 递增结果: {result1}")
        
        result2 = service_proxy.increment()
        print(f"[子进程] 递增结果: {result2}")
        
        print("[子进程] 测试2: 设置消息")
        old_msg = service_proxy.set_message("来自子进程的消息")
        print(f"[子进程] 旧消息: {old_msg}")
        
        print("[子进程] 测试3: 更新数据")
        service_proxy.update_data("modified_by", "child_process")
        service_proxy.update_data("timestamp", time.time())
        
        print("[子进程] 测试4: 获取最终状态")
        final_status = service_proxy.get_status()
        print(f"[子进程] 最终状态: {final_status}")
        
        print("[子进程] 所有测试完成")
        
    except Exception as e:
        print(f"[子进程] 错误: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("简单RPC功能测试")
    print("="*40)
    
    # 创建测试服务
    service = SimpleTestService()
    print(f"[主进程] 初始状态: counter={service.counter}, message='{service.message}'")
    
    # 创建RPC管理器
    config = proxy_module.ProxyConfiguration()
    config.debug_mode = True
    manager = proxy_module.SimpleRPCManager(config)
    
    # 启动管理器
    manager.start()
    print("[主进程] RPC管理器已启动")
    
    # 注册服务
    manager.register_object('test_service', service)
    print("[主进程] 服务已注册")
    
    # 记录初始状态
    initial_counter = service.counter
    initial_message = service.message
    initial_data = service.data.copy()
    
    # 启动子进程
    print("\n[主进程] 启动子进程...")
    process = multiprocessing.Process(target=child_process)
    process.start()
    process.join()
    
    print(f"\n[主进程] 子进程完成，退出码: {process.exitcode}")
    
    # 验证状态变化
    print("\n" + "="*40)
    print("状态验证")
    print("="*40)
    
    print("初始状态:")
    print(f"  计数器: {initial_counter}")
    print(f"  消息: '{initial_message}'")
    print(f"  数据: {initial_data}")
    
    print("\n最终状态:")
    print(f"  计数器: {service.counter}")
    print(f"  消息: '{service.message}'")
    print(f"  数据: {service.data}")
    
    # 验证变化
    counter_changed = service.counter != initial_counter
    message_changed = service.message != initial_message
    data_changed = service.data != initial_data
    
    print(f"\n验证结果:")
    print(f"  计数器已修改: {counter_changed} ✅" if counter_changed else "  计数器未修改: {counter_changed} ❌")
    print(f"  消息已修改: {message_changed} ✅" if message_changed else "  消息未修改: {message_changed} ❌")
    print(f"  数据已修改: {data_changed} ✅" if data_changed else "  数据未修改: {data_changed} ❌")
    
    all_passed = counter_changed and message_changed and data_changed
    print(f"\n🎉 测试结果: {'通过' if all_passed else '失败'}")
    
    if all_passed:
        print("✅ RPC功能正常！子进程成功调用主进程方法并修改了状态。")
    else:
        print("❌ RPC功能异常，请检查实现。")
    
    # 关闭管理器
    manager.shutdown()
    print("\n[主进程] RPC管理器已关闭")
    
    return 0 if all_passed else 1


if __name__ == "__main__":
    # Windows兼容性设置
    multiprocessing.set_start_method('spawn', force=True)
    
    exit_code = main()
    sys.exit(exit_code)
