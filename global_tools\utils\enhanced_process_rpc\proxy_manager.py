# -*- coding: utf-8 -*-
"""
ProcessProxy - 进程间通信代理模块
============================================

该模块实现了完整的进程间对象访问代理功能，允许子进程透明地访问主进程中的对象。

核心功能：
1. 真实访问主进程中的变量、函数和对象实例
2. 透明的方法调用机制，保持与本地调用相同的语法
3. 实时状态同步，确保主进程状态变化被子进程感知
4. 双向通信支持，完善的错误处理机制

技术架构：
- 基于multiprocessing.Manager的混合代理模式
- 智能通信方式选择（Manager代理 + Queue命令）
- 高效的状态缓存和同步机制
- 完善的错误处理和调试支持

使用示例：
---------
# 主进程中
proxy_manager = ProcessProxyManager()
proxy_manager.start()
proxy_manager.register_object('service', service_instance)

# 子进程中
proxy = ProcessProxy(proxy_manager, 'service')
result = proxy.method_name()  # 透明调用主进程方法

作者：Augment Agent
版本：1.0.0
"""

import multiprocessing
import threading
import traceback
import time
import queue
import pickle
import uuid
import os
from typing import Any, Dict, List, Optional, Callable, Union
from multiprocessing import Queue, Event, Lock

# 导入现有的基础设施
from global_tools.utils import Logger, Colors, ClassInstanceManager

# 获取日志记录器
logger: Logger = ClassInstanceManager.get_instance("ProcessProxyLogger")


class ProxyConfiguration:
    """
    代理配置类 - 提供灵活的配置选项

    该类管理ProcessProxy系统的所有配置参数，支持不同使用场景的优化配置。

    属性说明：
        cache_ttl (float): 缓存生存时间（秒），默认5.0秒
        batch_sync_threshold (int): 批量同步阈值，默认10个操作
        queue_timeout (float): 队列超时时间（秒），默认1.0秒
        debug_mode (bool): 调试模式开关，默认False
        performance_monitoring (bool): 性能监控开关，默认True
        max_retries (int): 最大重试次数，默认3次
        retry_delay (float): 重试延迟（秒），默认0.1秒

    使用示例：
        config = ProxyConfiguration()
        config.optimize_for_scenario('high_frequency')  # 优化为高频访问场景
        config.debug_mode = True  # 启用调试模式
    """

    def __init__(self):
        """初始化代理配置，设置所有参数的默认值"""
        self.cache_ttl = 5.0  # 缓存生存时间（秒）
        self.batch_sync_threshold = 10  # 批量同步阈值
        self.queue_timeout = 1.0  # 队列超时时间（秒）
        self.debug_mode = False  # 调试模式
        self.performance_monitoring = True  # 性能监控
        self.max_retries = 3  # 最大重试次数
        self.retry_delay = 0.1  # 重试延迟（秒）

        logger.debug("代理配置已初始化为默认值", color=Colors.DEBUG)

    def optimize_for_scenario(self, scenario: str):
        """
        根据使用场景自动优化配置

        Args:
            scenario (str): 场景类型，支持以下选项：
                - 'high_frequency': 高频访问场景，优化响应速度
                - 'large_data': 大数据传输场景，优化吞吐量
                - 'low_latency': 低延迟场景，最小化延迟
                - 'debug': 调试场景，启用详细日志和错误信息

        使用示例：
            config.optimize_for_scenario('high_frequency')
        """
        scenarios = {
            'high_frequency': {  # 高频访问场景
                'cache_ttl': 1.0,
                'batch_sync_threshold': 5,
                'queue_timeout': 0.5,
                'max_retries': 2
            },
            'large_data': {  # 大数据传输场景
                'cache_ttl': 10.0,
                'batch_sync_threshold': 20,
                'queue_timeout': 5.0,
                'max_retries': 5
            },
            'low_latency': {  # 低延迟场景
                'cache_ttl': 0.5,
                'batch_sync_threshold': 1,
                'queue_timeout': 0.1,
                'max_retries': 1
            },
            'debug': {  # 调试场景
                'debug_mode': True,
                'performance_monitoring': True,
                'cache_ttl': 2.0
            }
        }

        if scenario in scenarios:
            for key, value in scenarios[scenario].items():
                setattr(self, key, value)
            logger.info(f"代理配置已优化为 {scenario} 场景", color=Colors.INFO)
        else:
            logger.warning(f"未知的场景类型: {scenario}", color=Colors.WARNING)

    def validate_config(self) -> bool:
        """
        验证配置参数的有效性

        Returns:
            bool: 配置是否有效
        """
        try:
            assert self.cache_ttl > 0, "cache_ttl 必须大于0"
            assert self.batch_sync_threshold > 0, "batch_sync_threshold 必须大于0"
            assert self.queue_timeout > 0, "queue_timeout 必须大于0"
            assert self.max_retries >= 0, "max_retries 必须大于等于0"
            assert self.retry_delay >= 0, "retry_delay 必须大于等于0"

            logger.debug("配置验证通过", color=Colors.DEBUG)
            return True

        except AssertionError as e:
            logger.error(f"配置验证失败: {e}", color=Colors.ERROR)
            return False

    def to_dict(self) -> Dict[str, Any]:
        """
        将配置导出为字典格式

        Returns:
            Dict[str, Any]: 配置字典
        """
        return {
            'cache_ttl': self.cache_ttl,
            'batch_sync_threshold': self.batch_sync_threshold,
            'queue_timeout': self.queue_timeout,
            'debug_mode': self.debug_mode,
            'performance_monitoring': self.performance_monitoring,
            'max_retries': self.max_retries,
            'retry_delay': self.retry_delay
        }


class ErrorHandler:
    """
    错误处理器 - 完善的异常处理机制

    该类负责处理跨进程环境中的异常，提供详细的错误信息和调试支持。
    使用traceback.print_exc()输出详细的错误堆栈信息到控制台。

    功能特性：
    - 跨进程异常的序列化和传递
    - 详细的错误上下文信息收集
    - 支持调试模式和生产模式
    - 完整的错误堆栈信息输出

    使用示例：
        error_handler = ErrorHandler(debug_mode=True)
        try:
            # 一些可能出错的操作
            pass
        except Exception as e:
            error_info = error_handler.handle_cross_process_exception(e, context)
    """

    def __init__(self, debug_mode: bool = False):
        """
        初始化错误处理器

        Args:
            debug_mode (bool): 是否启用调试模式，调试模式下会输出更详细的错误信息
        """
        self.debug_mode = debug_mode
        self.__error_context = {}  # 错误上下文信息存储

        logger.debug(f"错误处理器已初始化，调试模式: {debug_mode}", color=Colors.DEBUG)

    def handle_cross_process_exception(self, exception: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理跨进程异常，收集完整的错误信息

        Args:
            exception (Exception): 发生的异常对象
            context (Dict[str, Any]): 异常发生时的上下文信息

        Returns:
            Dict[str, Any]: 包含完整错误信息的字典

        使用示例：
            context = {'obj_id': 'service', 'method_name': 'increment'}
            error_info = error_handler.handle_cross_process_exception(e, context)
        """
        # 收集完整的错误信息
        error_info = {
            'exception_type': type(exception).__name__,
            'exception_message': str(exception),
            'traceback': traceback.format_exc(),
            'process_id': os.getpid(),
            'timestamp': time.time(),
            'context': context
        }

        # 使用traceback.print_exc()输出到控制台
        print(f"[ProcessProxy Error] 进程 {os.getpid()} 发生异常:")
        traceback.print_exc()

        # 调试模式下输出更详细的信息
        if self.debug_mode:
            logger.error(f"跨进程异常详情: {error_info}", color=Colors.ERROR)
            print(f"[Debug] 异常上下文: {context}")

        # 存储错误上下文用于后续分析
        error_id = str(uuid.uuid4())
        self.__error_context[error_id] = error_info
        error_info['error_id'] = error_id

        return error_info

    def create_serializable_exception(self, error_info: Dict[str, Any]) -> Exception:
        """
        创建可序列化的异常对象，用于跨进程传递

        Args:
            error_info (Dict[str, Any]): 错误信息字典

        Returns:
            Exception: 可序列化的异常对象
        """
        class ProxyException(Exception):
            """代理异常类，用于跨进程异常传递"""
            def __init__(self, error_info):
                self.error_info = error_info
                super().__init__(error_info['exception_message'])

            def __str__(self):
                return f"[ProcessProxy] {self.error_info['exception_type']}: {self.error_info['exception_message']}"

        return ProxyException(error_info)

    def log_error(self, error_info: Dict[str, Any]):
        """
        记录错误信息到日志系统

        Args:
            error_info (Dict[str, Any]): 错误信息字典
        """
        logger.error(
            f"进程 {error_info['process_id']} 异常: {error_info['exception_type']} - {error_info['exception_message']}",
            color=Colors.ERROR
        )

        if self.debug_mode:
            logger.debug(f"错误堆栈: {error_info['traceback']}", color=Colors.DEBUG)

    def format_cross_process_error(self, error_info: Dict[str, Any]) -> str:
        """
        格式化跨进程错误信息，提供清晰的错误展示

        Args:
            error_info (Dict[str, Any]): 错误信息字典

        Returns:
            str: 格式化后的错误信息字符串

        使用示例：
            formatted_error = error_handler.format_cross_process_error(error_info)
            print(formatted_error)
        """
        try:
            # 提取关键错误信息
            exception_type = error_info.get('exception_type', 'Unknown')
            exception_message = error_info.get('exception_message', '未知错误')
            process_id = error_info.get('process_id', 'Unknown')
            context = error_info.get('context', {})
            traceback_str = error_info.get('traceback', '')

            # 构建格式化的错误信息
            formatted_lines = [
                "=" * 80,
                f"[跨进程RPC错误] 进程 {process_id} 中发生异常",
                "=" * 80,
                f"异常类型: {exception_type}",
                f"异常消息: {exception_message}",
                ""
            ]

            # 添加上下文信息
            if context:
                formatted_lines.append("调用上下文:")
                for key, value in context.items():
                    formatted_lines.append(f"  {key}: {value}")
                formatted_lines.append("")

            # 添加详细的堆栈信息
            if traceback_str:
                formatted_lines.append("详细堆栈信息:")
                formatted_lines.append(traceback_str)

            formatted_lines.append("=" * 80)

            return "\n".join(formatted_lines)

        except Exception as e:
            # 如果格式化失败，返回基本错误信息
            return f"[错误格式化失败] 原始错误: {error_info}, 格式化错误: {e}"

    def create_enhanced_exception(self, error_info: Dict[str, Any]) -> Exception:
        """
        创建增强的跨进程异常，保持原始错误信息的完整性

        Args:
            error_info (Dict[str, Any]): 错误信息字典

        Returns:
            Exception: 增强的跨进程异常对象

        使用示例：
            enhanced_exception = error_handler.create_enhanced_exception(error_info)
            raise enhanced_exception
        """
        try:
            # 创建增强的跨进程异常
            return CrossProcessException(error_info)
        except Exception as e:
            # 如果创建失败，返回基本异常
            return Exception(f"跨进程异常创建失败: {e}, 原始错误: {error_info}")

    def get_error_statistics(self) -> Dict[str, Any]:
        """
        获取错误统计信息

        Returns:
            Dict[str, Any]: 错误统计数据
        """
        error_types = {}
        for error_info in self.__error_context.values():
            error_type = error_info['exception_type']
            error_types[error_type] = error_types.get(error_type, 0) + 1

        return {
            'total_errors': len(self.__error_context),
            'error_types': error_types,
            'recent_errors': list(self.__error_context.values())[-10:]  # 最近10个错误
        }


class CrossProcessException(Exception):
    """
    跨进程异常类 - 专门处理跨进程调用中的异常

    该类专门用于处理跨进程RPC调用中发生的异常，保持原始错误信息的完整性，
    并提供清晰的错误展示格式。

    功能特性：
    - 保持原始异常的完整信息
    - 提供清晰的错误展示格式
    - 支持错误上下文信息
    - 集成traceback信息展示

    使用示例：
        error_info = {
            'exception_type': 'ValueError',
            'exception_message': '参数错误',
            'traceback': '...',
            'context': {'obj_id': 'service', 'method_name': 'test'}
        }
        exception = CrossProcessException(error_info)
        raise exception
    """

    def __init__(self, error_info: Dict[str, Any]):
        """
        初始化跨进程异常

        Args:
            error_info (Dict[str, Any]): 完整的错误信息字典

        使用示例：
            exception = CrossProcessException(error_info)
        """
        self.error_info = error_info
        self.__exception_type = error_info.get('exception_type', 'Unknown')
        self.__exception_message = error_info.get('exception_message', '未知错误')
        self.__process_id = error_info.get('process_id', 'Unknown')
        self.__context = error_info.get('context', {})
        self.__traceback_str = error_info.get('traceback', '')

        # 调用父类构造函数
        super().__init__(self.__format_error_message())

        # 输出详细的错误信息到控制台
        self.__print_detailed_error()

    def __format_error_message(self) -> str:
        """
        格式化错误消息（私有方法）

        Returns:
            str: 格式化后的错误消息
        """
        context_str = ""
        if self.__context:
            context_parts = [f"{k}={v}" for k, v in self.__context.items()]
            context_str = f" (上下文: {', '.join(context_parts)})"

        return f"[跨进程RPC] {self.__exception_type}: {self.__exception_message}{context_str}"

    def __print_detailed_error(self):
        """
        输出详细的错误信息到控制台（私有方法）

        使用traceback.print_exc()等效的详细输出机制
        """
        print("\n" + "=" * 80)
        print(f"[跨进程RPC错误] 进程 {self.__process_id} 中发生异常")
        print("=" * 80)
        print(f"异常类型: {self.__exception_type}")
        print(f"异常消息: {self.__exception_message}")

        # 输出上下文信息
        if self.__context:
            print("\n调用上下文:")
            for key, value in self.__context.items():
                print(f"  {key}: {value}")

        # 输出详细的堆栈信息
        if self.__traceback_str:
            print("\n原始错误堆栈信息:")
            print(self.__traceback_str)

        print("=" * 80 + "\n")

    def get_original_exception_type(self) -> str:
        """
        获取原始异常类型

        Returns:
            str: 原始异常类型名称

        使用示例：
            exception_type = cross_process_exception.get_original_exception_type()
        """
        return self.__exception_type

    def get_original_message(self) -> str:
        """
        获取原始异常消息

        Returns:
            str: 原始异常消息

        使用示例：
            message = cross_process_exception.get_original_message()
        """
        return self.__exception_message

    def get_context(self) -> Dict[str, Any]:
        """
        获取错误上下文信息

        Returns:
            Dict[str, Any]: 错误上下文字典

        使用示例：
            context = cross_process_exception.get_context()
        """
        return self.__context.copy()

    def get_traceback_string(self) -> str:
        """
        获取原始错误的traceback字符串

        Returns:
            str: traceback字符串

        使用示例：
            traceback_str = cross_process_exception.get_traceback_string()
        """
        return self.__traceback_str


class PerformanceMonitor:
    """
    性能监控器 - 监控代理调用的性能指标

    该类负责收集和分析ProcessProxy系统的性能数据，提供性能优化建议。

    功能特性：
    - 线程安全的性能数据收集
    - 实时性能统计和历史数据记录
    - 基于数据的性能优化建议
    - 详细的性能报告生成

    监控指标：
    - 方法调用延迟和成功率
    - 调用频率和错误率统计
    - 性能趋势分析

    使用示例：
        monitor = PerformanceMonitor()
        monitor.record_call('service.increment', 0.05, True)
        suggestions = monitor.get_optimization_suggestions()
    """

    def __init__(self):
        """初始化性能监控器"""
        self.__call_stats = {}  # 调用统计数据
        self.__latency_history = {}  # 延迟历史记录
        self.__error_rates = {}  # 错误率统计
        self.__lock = threading.Lock()  # 线程安全锁

        logger.debug("性能监控器已初始化", color=Colors.DEBUG)

    def record_call(self, method_name: str, latency: float, success: bool):
        """
        记录方法调用性能数据

        Args:
            method_name (str): 方法名称，格式为 'obj_id.method_name'
            latency (float): 调用延迟（秒）
            success (bool): 调用是否成功

        使用示例：
            monitor.record_call('service.increment', 0.05, True)
            monitor.record_call('db.query', 1.2, False)
        """
        with self.__lock:
            # 初始化统计数据结构
            if method_name not in self.__call_stats:
                self.__call_stats[method_name] = {
                    'total_calls': 0,
                    'success_calls': 0,
                    'total_latency': 0.0,
                    'min_latency': float('inf'),
                    'max_latency': 0.0,
                    'last_call_time': 0.0
                }

            # 更新统计数据
            stats = self.__call_stats[method_name]
            stats['total_calls'] += 1
            stats['last_call_time'] = time.time()

            if success:
                stats['success_calls'] += 1

            stats['total_latency'] += latency
            stats['min_latency'] = min(stats['min_latency'], latency)
            stats['max_latency'] = max(stats['max_latency'], latency)

            # 记录延迟历史（保留最近100次调用）
            if method_name not in self.__latency_history:
                self.__latency_history[method_name] = []

            history = self.__latency_history[method_name]
            history.append({
                'latency': latency,
                'success': success,
                'timestamp': time.time()
            })

            # 限制历史记录长度
            if len(history) > 100:
                history.pop(0)

    def get_optimization_suggestions(self) -> Dict[str, str]:
        """
        基于性能数据提供优化建议

        Returns:
            Dict[str, str]: 方法名到优化建议的映射

        使用示例：
            suggestions = monitor.get_optimization_suggestions()
            for method, suggestion in suggestions.items():
                print(f"{method}: {suggestion}")
        """
        suggestions = {}

        with self.__lock:
            for method_name, stats in self.__call_stats.items():
                if stats['total_calls'] < 5:  # 数据不足，跳过
                    continue

                # 计算平均延迟和成功率
                avg_latency = stats['total_latency'] / stats['total_calls']
                success_rate = stats['success_calls'] / stats['total_calls']

                # 基于延迟的建议
                if avg_latency > 2.0:
                    suggestions[method_name] = "平均延迟过高，考虑使用缓存或优化算法"
                elif avg_latency > 1.0:
                    suggestions[method_name] = "延迟较高，建议检查网络连接或使用批量操作"

                # 基于成功率的建议
                if success_rate < 0.8:
                    suggestions[method_name] = "成功率较低，检查错误处理和重试机制"
                elif success_rate < 0.95:
                    suggestions[method_name] = "偶有失败，建议增加错误监控"

                # 基于调用频率的建议
                if stats['total_calls'] > 1000 and avg_latency > 0.1:
                    suggestions[method_name] = "高频调用且有延迟，强烈建议使用缓存"

        return suggestions

    def get_performance_report(self) -> Dict[str, Any]:
        """
        生成详细的性能报告

        Returns:
            Dict[str, Any]: 包含完整性能数据的报告
        """
        with self.__lock:
            report = {
                'summary': {
                    'total_methods': len(self.__call_stats),
                    'total_calls': sum(stats['total_calls'] for stats in self.__call_stats.values()),
                    'total_success_calls': sum(stats['success_calls'] for stats in self.__call_stats.values())
                },
                'method_details': {},
                'top_slow_methods': [],
                'top_error_methods': []
            }

            # 计算总体成功率
            if report['summary']['total_calls'] > 0:
                report['summary']['overall_success_rate'] = (
                    report['summary']['total_success_calls'] / report['summary']['total_calls']
                )
            else:
                report['summary']['overall_success_rate'] = 1.0

            # 详细方法统计
            method_performance = []
            for method_name, stats in self.__call_stats.items():
                if stats['total_calls'] > 0:
                    avg_latency = stats['total_latency'] / stats['total_calls']
                    success_rate = stats['success_calls'] / stats['total_calls']

                    method_detail = {
                        'method_name': method_name,
                        'total_calls': stats['total_calls'],
                        'success_rate': success_rate,
                        'avg_latency': avg_latency,
                        'min_latency': stats['min_latency'],
                        'max_latency': stats['max_latency'],
                        'last_call_time': stats['last_call_time']
                    }

                    report['method_details'][method_name] = method_detail
                    method_performance.append(method_detail)

            # 找出最慢的方法（按平均延迟排序）
            report['top_slow_methods'] = sorted(
                method_performance,
                key=lambda x: x['avg_latency'],
                reverse=True
            )[:5]

            # 找出错误率最高的方法
            report['top_error_methods'] = sorted(
                method_performance,
                key=lambda x: 1 - x['success_rate'],
                reverse=True
            )[:5]

            return report


# StateManager类已移除 - 将由BasicCache替代


class BasicCache:
    """
    基础缓存 - 简单的TTL缓存机制

    该类提供简单的缓存功能，替代复杂的StateManager，
    专注于基础的缓存存储和TTL（生存时间）管理。

    功能特性：
    - 简单的TTL缓存机制
    - 线程安全的缓存操作
    - 自动过期清理
    - 内存使用优化

    使用示例：
        cache = BasicCache(ttl=5.0)
        cache.set('key', 'value')
        value = cache.get('key')
    """

    def __init__(self, ttl: float = 5.0):
        """
        初始化基础缓存

        Args:
            ttl (float): 缓存生存时间（秒），默认5.0秒

        使用示例：
            cache = BasicCache(ttl=10.0)  # 10秒TTL
        """
        self.ttl = ttl
        self.__cache = {}  # 缓存数据 {key: value}
        self.__timestamps = {}  # 缓存时间戳 {key: timestamp}
        self.__lock = threading.Lock()  # 线程安全锁

        logger.debug(f"基础缓存已初始化，TTL: {ttl}秒", color=Colors.DEBUG)

    def get(self, key: str) -> Any:
        """
        从缓存获取值

        Args:
            key (str): 缓存键

        Returns:
            Any: 缓存的值，如果缓存无效或不存在则返回None

        使用示例：
            value = cache.get('my_key')
            if value is not None:
                # 使用缓存值
                pass
        """
        with self.__lock:
            # 检查缓存是否存在
            if key not in self.__cache:
                return None

            # 检查TTL
            cache_time = self.__timestamps[key]
            if (time.time() - cache_time) >= self.ttl:
                # 缓存已过期，清理
                self.__cache.pop(key, None)
                self.__timestamps.pop(key, None)
                logger.debug(f"缓存已过期并清理: {key}", color=Colors.DEBUG)
                return None

            logger.debug(f"缓存命中: {key}", color=Colors.DEBUG)
            return self.__cache[key]

    def set(self, key: str, value: Any):
        """
        设置缓存值

        Args:
            key (str): 缓存键
            value (Any): 要缓存的值

        使用示例：
            cache.set('my_key', 'my_value')
        """
        with self.__lock:
            self.__cache[key] = value
            self.__timestamps[key] = time.time()

        logger.debug(f"缓存已设置: {key}", color=Colors.DEBUG)

    def invalidate(self, key: str):
        """
        使缓存失效

        Args:
            key (str): 要失效的缓存键

        使用示例：
            cache.invalidate('my_key')
        """
        with self.__lock:
            self.__cache.pop(key, None)
            self.__timestamps.pop(key, None)

        logger.debug(f"缓存已失效: {key}", color=Colors.DEBUG)

    def clear(self):
        """
        清空所有缓存

        使用示例：
            cache.clear()
        """
        with self.__lock:
            cache_count = len(self.__cache)
            self.__cache.clear()
            self.__timestamps.clear()

        logger.debug(f"缓存已清空，清理了 {cache_count} 个缓存项", color=Colors.DEBUG)

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取缓存统计信息

        Returns:
            Dict[str, Any]: 缓存统计数据
        """
        with self.__lock:
            total_items = len(self.__cache)
            valid_items = 0
            current_time = time.time()

            # 计算有效缓存项数量
            for key, timestamp in self.__timestamps.items():
                if (current_time - timestamp) < self.ttl:
                    valid_items += 1

            return {
                'total_items': total_items,
                'valid_items': valid_items,
                'expired_items': total_items - valid_items,
                'hit_rate': valid_items / max(total_items, 1),
                'ttl': self.ttl
            }


class CommandProcessor:
    """
    命令处理器 - 处理Queue模式的方法调用

    该类负责在主进程中处理来自子进程的方法调用命令，提供可靠的异步执行机制。

    功能特性：
    - 多线程命令处理和队列管理
    - 支持同步命令执行和响应
    - 实现命令超时和重试机制
    - 提供命令执行状态监控

    工作原理：
    1. 子进程将命令放入命令队列
    2. 主进程的工作线程从队列取出命令
    3. 在主进程中执行命令并返回结果
    4. 通过响应队列将结果发送回子进程

    使用示例：
        processor = CommandProcessor(target_objects, config, error_handler)
        processor.start()
        result = processor.execute_command('service', 'increment', (), {})
    """

    def __init__(self, target_objects: Dict[str, Any], config: ProxyConfiguration, error_handler: ErrorHandler):
        """
        初始化命令处理器

        Args:
            target_objects (Dict[str, Any]): 目标对象字典 {obj_id: obj_instance}
            config (ProxyConfiguration): 代理配置对象
            error_handler (ErrorHandler): 错误处理器
        """
        self.target_objects = target_objects
        self.config = config
        self.error_handler = error_handler
        self.__command_queue = Queue()  # 命令队列
        self.__response_queues = {}  # 响应队列字典 {call_id: queue}
        self.__worker_thread = None  # 工作线程
        self.__running = False  # 运行状态标志
        self.__lock = threading.Lock()  # 线程安全锁

        logger.debug("命令处理器已初始化", color=Colors.DEBUG)

    def start(self):
        """
        启动命令处理器

        启动后台工作线程开始处理命令队列中的命令。

        使用示例：
            processor.start()
        """
        if not self.__running:
            self.__running = True
            self.__worker_thread = threading.Thread(target=self.__process_commands, daemon=True)
            self.__worker_thread.start()

            # 启动跨进程命令处理线程（非daemon线程，确保正确清理）
            self.__cross_process_thread = threading.Thread(target=self.__process_cross_process_commands, daemon=False)
            self.__cross_process_thread.start()

            logger.info("命令处理器已启动", color=Colors.INFO)
        else:
            logger.warning("命令处理器已经在运行", color=Colors.WARNING)

    def stop(self):
        """
        停止命令处理器

        停止后台工作线程并清理资源。

        使用示例：
            processor.stop()
        """
        if self.__running:
            self.__running = False

            # 等待工作线程结束
            if self.__worker_thread and self.__worker_thread.is_alive():
                self.__worker_thread.join(timeout=2.0)

            # 等待跨进程线程结束
            if hasattr(self, '_CommandProcessor__cross_process_thread') and self.__cross_process_thread.is_alive():
                self.__cross_process_thread.join(timeout=2.0)

            logger.info("命令处理器已停止", color=Colors.INFO)
        else:
            logger.warning("命令处理器未在运行", color=Colors.WARNING)

    def execute_command(self, obj_id: str, method_name: str, args: tuple, kwargs: dict) -> Any:
        """
        执行命令（同步调用）

        Args:
            obj_id (str): 对象ID
            method_name (str): 方法名称
            args (tuple): 位置参数
            kwargs (dict): 关键字参数

        Returns:
            Any: 方法调用结果

        Raises:
            Exception: 如果命令执行失败

        使用示例：
            result = processor.execute_command('service', 'increment', (), {})
        """
        if not self.__running:
            raise RuntimeError("命令处理器未启动")

        call_id = str(uuid.uuid4())
        response_queue = Queue()

        # 注册响应队列
        with self.__lock:
            self.__response_queues[call_id] = response_queue

        try:
            # 构建命令
            command = {
                'call_id': call_id,
                'obj_id': obj_id,
                'method_name': method_name,
                'args': args,
                'kwargs': kwargs,
                'timestamp': time.time()
            }

            # 发送命令到队列
            self.__command_queue.put(command, timeout=self.config.queue_timeout)
            logger.debug(f"命令已发送: {obj_id}.{method_name}", color=Colors.DEBUG)

            # 等待响应
            response = response_queue.get(timeout=self.config.queue_timeout * 2)

            # 处理响应
            if 'error' in response:
                # 重新抛出异常
                error_info = response['error']
                raise self.error_handler.create_serializable_exception(error_info)

            logger.debug(f"命令执行成功: {obj_id}.{method_name}", color=Colors.DEBUG)
            return response['result']

        except queue.Empty:
            error_msg = f"命令执行超时: {obj_id}.{method_name}"
            logger.error(error_msg, color=Colors.ERROR)
            raise TimeoutError(error_msg)
        finally:
            # 清理响应队列
            with self.__lock:
                self.__response_queues.pop(call_id, None)

    def __process_commands(self):
        """
        命令处理主循环（私有方法）

        在后台线程中运行，持续处理命令队列中的命令。
        """
        logger.debug("命令处理主循环已启动", color=Colors.DEBUG)

        while self.__running:
            try:
                # 从队列获取命令（带超时）
                command = self.__command_queue.get(timeout=0.1)
                self.__handle_command(command)
            except queue.Empty:
                # 超时是正常的，继续循环
                continue
            except Exception as e:
                logger.error(f"命令处理循环异常: {e}", color=Colors.ERROR)
                traceback.print_exc()

        logger.debug("命令处理主循环已退出", color=Colors.DEBUG)

    def __process_cross_process_commands(self):
        """
        跨进程命令处理主循环（私有方法）

        监听共享的命令队列，处理来自子进程的命令。
        """
        logger.debug("跨进程命令处理主循环已启动", color=Colors.DEBUG)

        while self.__running:
            try:
                # 获取共享队列
                command_queue, response_queue = SimpleRPCManager.get_shared_queues()
                if command_queue is None or response_queue is None:
                    time.sleep(0.1)
                    continue

                # 从共享命令队列获取命令
                try:
                    command = command_queue.get(timeout=0.1)
                    logger.debug(f"收到跨进程命令: {command.get('call_id')}", color=Colors.DEBUG)

                    # 处理命令并发送响应
                    self.__handle_cross_process_command(command, response_queue)

                except queue.Empty:
                    continue

            except Exception as e:
                logger.error(f"跨进程命令处理异常: {e}", color=Colors.ERROR)
                traceback.print_exc()

        logger.debug("跨进程命令处理主循环已退出", color=Colors.DEBUG)

    def __handle_cross_process_command(self, command: dict, response_queue: Queue):
        """
        处理跨进程命令（私有方法）

        Args:
            command (dict): 命令字典
            response_queue (Queue): 响应队列
        """
        call_id = command.get('call_id')
        obj_id = command.get('obj_id')
        method_name = command.get('method_name')
        args = command.get('args', ())
        kwargs = command.get('kwargs', {})

        try:
            # 检查对象是否存在
            if obj_id not in self.target_objects:
                raise KeyError(f"对象 '{obj_id}' 未注册")

            # 获取目标对象
            target_obj = self.target_objects[obj_id]

            # 检查方法是否存在
            if not hasattr(target_obj, method_name):
                raise AttributeError(f"对象 '{obj_id}' 没有方法 '{method_name}'")

            # 执行方法
            method = getattr(target_obj, method_name)
            result = method(*args, **kwargs)

            # 发送成功响应
            response = {
                'call_id': call_id,
                'result': result,
                'timestamp': time.time()
            }
            response_queue.put(response)

            logger.debug(f"跨进程命令执行成功: {obj_id}.{method_name}", color=Colors.DEBUG)

        except Exception as e:
            # 发送错误响应
            error_info = self.error_handler.handle_cross_process_exception(e, {
                'obj_id': obj_id,
                'method_name': method_name,
                'call_id': call_id
            })

            response = {
                'call_id': call_id,
                'error': str(error_info),
                'timestamp': time.time()
            }
            response_queue.put(response)

            logger.error(f"跨进程命令执行失败: {obj_id}.{method_name}, 错误: {e}", color=Colors.ERROR)

    def __handle_command(self, command: Dict[str, Any]):
        """
        处理单个命令（私有方法）

        Args:
            command (Dict[str, Any]): 命令字典
        """
        call_id = command['call_id']
        obj_id = command['obj_id']
        method_name = command['method_name']
        args = command['args']
        kwargs = command['kwargs']

        logger.debug(f"处理命令: {obj_id}.{method_name}", color=Colors.DEBUG)

        try:
            # 验证对象是否存在
            if obj_id not in self.target_objects:
                raise ValueError(f"对象 '{obj_id}' 未注册")

            target_obj = self.target_objects[obj_id]

            # 验证方法是否存在
            if not hasattr(target_obj, method_name):
                raise AttributeError(f"对象 '{obj_id}' 没有方法 '{method_name}'")

            # 执行方法调用
            method = getattr(target_obj, method_name)
            result = method(*args, **kwargs)

            # 构建成功响应
            response = {'result': result}
            logger.debug(f"命令执行成功: {obj_id}.{method_name}", color=Colors.DEBUG)

        except Exception as e:
            # 处理异常
            context = {
                'obj_id': obj_id,
                'method_name': method_name,
                'args': args,
                'kwargs': kwargs,
                'call_id': call_id
            }
            error_info = self.error_handler.handle_cross_process_exception(e, context)
            response = {'error': error_info}
            logger.error(f"命令执行失败: {obj_id}.{method_name}, 错误: {e}", color=Colors.ERROR)

        # 发送响应
        self.__send_response(call_id, response)

    def __send_response(self, call_id: str, response: Dict[str, Any]):
        """
        发送响应到对应的响应队列（私有方法）

        Args:
            call_id (str): 调用ID
            response (Dict[str, Any]): 响应数据
        """
        with self.__lock:
            if call_id in self.__response_queues:
                try:
                    self.__response_queues[call_id].put(response, timeout=self.config.queue_timeout)
                    logger.debug(f"响应已发送: {call_id}", color=Colors.DEBUG)
                except queue.Full:
                    logger.error(f"响应队列已满，调用ID: {call_id}", color=Colors.ERROR)
            else:
                logger.warning(f"未找到响应队列，调用ID: {call_id}", color=Colors.WARNING)

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取命令处理器统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        with self.__lock:
            return {
                'running': self.__running,
                'command_queue_size': self.__command_queue.qsize() if hasattr(self.__command_queue, 'qsize') else 0,
                'active_response_queues': len(self.__response_queues),
                'registered_objects': list(self.target_objects.keys())
            }


# Manager相关代码已移除 - 使用纯RPC通信机制


# ProcessProxyManager类已移除 - 将由SimpleRPCManager替代

class SimpleRPCManager:
    """
    简化的RPC管理器 - 纯Queue RPC通信机制

    该类专注于Queue RPC通信，移除了所有Manager代理相关的复杂逻辑，
    提供简洁、高效、易维护的跨进程对象访问功能。

    功能特性：
    - 纯Queue RPC通信机制
    - 简化的对象注册和管理
    - 完善的错误处理和日志记录
    - 高效的命令处理和响应
    - 支持跨进程Queue通信

    使用示例：
        # 主进程中
        manager = SimpleRPCManager()
        manager.start()
        manager.register_object('service', service_instance)

        # 子进程中
        manager = SimpleRPCManager(command_queue=shared_queue, response_queue=shared_response_queue)
        proxy = manager.create_proxy('service')
        result = proxy.some_method()
    """

    # 类级别的共享队列和Manager
    _shared_command_queue = None
    _shared_response_queue = None
    _shared_manager = None

    def __init__(self, config: ProxyConfiguration = None, command_queue=None, response_queue=None):
        """
        初始化简化RPC管理器

        Args:
            config (ProxyConfiguration, optional): 代理配置，如果为None则使用默认配置
            command_queue (Queue, optional): 外部命令队列，用于子进程
            response_queue (Queue, optional): 外部响应队列，用于子进程

        使用示例：
            # 主进程 - 使用默认配置
            manager = SimpleRPCManager()

            # 子进程 - 使用共享队列
            manager = SimpleRPCManager(
                command_queue=shared_command_queue,
                response_queue=shared_response_queue
            )
        """
        # 初始化配置
        self.config = config if config is not None else ProxyConfiguration()
        if not self.config.validate_config():
            raise ValueError("代理配置验证失败")

        # 初始化组件
        self.__registered_objects = {}  # 注册的对象 {obj_id: obj_instance}
        self.__error_handler = ErrorHandler(self.config.debug_mode)
        self.__performance_monitor = PerformanceMonitor() if self.config.performance_monitoring else None
        self.__command_processor = None  # 延迟初始化
        self.__is_running = False  # 运行状态标志

        # 外部队列（用于子进程）
        self.__external_command_queue = command_queue
        self.__external_response_queue = response_queue
        self.__is_child_process = command_queue is not None

        logger.info("简化RPC管理器已初始化", color=Colors.INFO)

    @classmethod
    def get_shared_queues(cls):
        """
        获取共享的队列（用于子进程）

        Returns:
            tuple: (command_queue, response_queue)
        """
        return cls._shared_command_queue, cls._shared_response_queue

    def register_object(self, obj_id: str, obj_instance: Any):
        """
        注册对象，使其可以被子进程通过RPC访问

        Args:
            obj_id (str): 对象ID，用于在子进程中引用该对象
            obj_instance (Any): 要注册的对象实例

        Raises:
            ValueError: 如果obj_id已存在或obj_instance为None

        使用示例：
            service = MyService()
            manager.register_object('service', service)

            db = DatabaseManager()
            manager.register_object('database', db)
        """
        logger.info(f"开始注册对象: {obj_id}, 类型: {type(obj_instance)}", color=Colors.INFO)

        if not obj_id:
            raise ValueError("obj_id 不能为空")
        if obj_instance is None:
            raise ValueError("obj_instance 不能为None")
        if obj_id in self.__registered_objects:
            raise ValueError(f"对象ID '{obj_id}' 已存在")

        # 注册对象到本地字典
        self.__registered_objects[obj_id] = obj_instance

        # 如果命令处理器已初始化，更新其目标对象
        if self.__command_processor is not None:
            self.__command_processor.target_objects[obj_id] = obj_instance

        logger.info(f"对象已注册: {obj_id} -> {type(obj_instance).__name__}", color=Colors.INFO)

    def unregister_object(self, obj_id: str):
        """
        注销对象

        Args:
            obj_id (str): 要注销的对象ID

        Raises:
            KeyError: 如果obj_id不存在

        使用示例：
            manager.unregister_object('service')
        """
        if obj_id not in self.__registered_objects:
            raise KeyError(f"对象ID '{obj_id}' 不存在")

        # 从注册表中移除
        del self.__registered_objects[obj_id]

        # 从命令处理器中移除
        if self.__command_processor is not None:
            self.__command_processor.target_objects.pop(obj_id, None)

        logger.info(f"对象已注销: {obj_id}", color=Colors.INFO)

    def get_registered_objects(self) -> List[str]:
        """
        获取已注册对象列表

        Returns:
            List[str]: 已注册的对象ID列表

        使用示例：
            objects = manager.get_registered_objects()
            print(f"已注册对象: {objects}")
        """
        return list(self.__registered_objects.keys())

    def start(self):
        """
        启动RPC管理器

        启动命令处理器开始处理RPC请求。

        使用示例：
            manager.start()
        """
        if self.__is_running:
            logger.warning("RPC管理器已经在运行", color=Colors.WARNING)
            return

        try:
            # 如果是子进程模式，直接标记为运行状态
            if self.__is_child_process:
                self.__is_running = True
                logger.info("子进程RPC管理器已启动（使用外部队列）", color=Colors.SUCCESS)
                return

            # 主进程模式：创建multiprocessing.Manager和共享队列
            if SimpleRPCManager._shared_command_queue is None:
                # 创建multiprocessing.Manager
                SimpleRPCManager._shared_manager = multiprocessing.Manager()
                SimpleRPCManager._shared_command_queue = SimpleRPCManager._shared_manager.Queue()
                SimpleRPCManager._shared_response_queue = SimpleRPCManager._shared_manager.Queue()
                logger.debug("跨进程共享队列已创建", color=Colors.DEBUG)

            # 初始化并启动命令处理器
            if self.__command_processor is None:
                self.__command_processor = CommandProcessor(
                    self.__registered_objects,
                    self.config,
                    self.__error_handler
                )

            # 启动命令处理器
            self.__command_processor.start()
            self.__is_running = True

            logger.info("简化RPC管理器已启动", color=Colors.SUCCESS)

        except Exception as e:
            logger.error(f"启动RPC管理器失败: {e}", color=Colors.ERROR)
            traceback.print_exc()
            raise

    def shutdown(self):
        """
        关闭RPC管理器

        停止命令处理器并清理资源。

        使用示例：
            manager.shutdown()
        """
        if not self.__is_running:
            logger.warning("RPC管理器未在运行", color=Colors.WARNING)
            return

        try:
            # 停止命令处理器
            if self.__command_processor is not None:
                self.__command_processor.stop()

            # 清理共享Manager（只在主进程中）
            if not self.__is_child_process and SimpleRPCManager._shared_manager is not None:
                try:
                    SimpleRPCManager._shared_manager.shutdown()
                    SimpleRPCManager._shared_manager = None
                    SimpleRPCManager._shared_command_queue = None
                    SimpleRPCManager._shared_response_queue = None
                    logger.debug("共享Manager已清理", color=Colors.DEBUG)
                except Exception as e:
                    logger.warning(f"清理共享Manager时出现警告: {e}", color=Colors.WARNING)

            self.__is_running = False
            logger.info("简化RPC管理器已关闭", color=Colors.INFO)

        except Exception as e:
            logger.error(f"关闭RPC管理器失败: {e}", color=Colors.ERROR)
            traceback.print_exc()

    def create_proxy(self, obj_id: str) -> 'ProcessProxy':
        """
        创建代理对象

        Args:
            obj_id (str): 要代理的对象ID

        Returns:
            ProcessProxy: 代理对象实例

        Raises:
            RuntimeError: 如果管理器未启动

        使用示例：
            proxy = manager.create_proxy('service')
            result = proxy.some_method()
        """
        if not self.__is_running:
            raise RuntimeError("RPC管理器未启动，请先调用start()方法")

        # 子进程模式下不需要检查对象是否存在，因为对象在主进程中
        if not self.__is_child_process and obj_id not in self.__registered_objects:
            raise KeyError(f"对象ID '{obj_id}' 不存在")

        # 创建代理对象
        proxy = ProcessProxy(self, obj_id)
        logger.debug(f"代理对象已创建: {obj_id}", color=Colors.DEBUG)
        return proxy

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取管理器统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = {
            'is_running': self.__is_running,
            'registered_objects': len(self.__registered_objects),
            'object_list': list(self.__registered_objects.keys()),
            'config': self.config.to_dict()
        }

        # 添加命令处理器统计
        if self.__command_processor is not None:
            stats['command_processor'] = self.__command_processor.get_statistics()

        # 添加性能监控统计
        if self.__performance_monitor is not None:
            stats['performance'] = self.__performance_monitor.get_performance_report()

        return stats


class ProcessProxy:
    """
    进程代理类 - 主代理类

    该类是ProcessProxy系统的核心，提供透明的跨进程对象访问接口。
    通过实现Python的魔术方法，使得在子进程中使用代理对象就像使用本地对象一样。

    功能特性：
    - 实现完整的魔术方法支持
    - 透明的属性访问和方法调用
    - 集成智能缓存和状态同步
    - 提供与原对象相同的使用体验
    - 支持链式调用和复杂操作

    工作原理：
    1. 拦截所有属性访问和方法调用
    2. 通过CallDispatcher选择最优通信方式
    3. 将调用转发到主进程中的真实对象
    4. 返回结果并更新本地缓存

    使用示例：
        # 在子进程中
        proxy = ProcessProxy(manager, 'service')

        # 像使用本地对象一样使用代理
        result = proxy.increment()  # 方法调用
        data = proxy.data  # 属性访问
        proxy.status = 'active'  # 属性设置
        length = len(proxy.items)  # 魔术方法
    """

    def __init__(self, rpc_manager: SimpleRPCManager, obj_id: str):
        """
        初始化进程代理对象

        Args:
            rpc_manager (SimpleRPCManager): RPC管理器实例
            obj_id (str): 目标对象ID

        使用示例：
            proxy = ProcessProxy(manager, 'service')
        """
        # 使用object.__setattr__避免触发自定义的__setattr__
        object.__setattr__(self, '_ProcessProxy__rpc_manager', rpc_manager)
        object.__setattr__(self, '_ProcessProxy__obj_id', obj_id)
        object.__setattr__(self, '_ProcessProxy__initialized', False)

        # 初始化基础缓存
        object.__setattr__(self, '_ProcessProxy__cache', BasicCache())

        object.__setattr__(self, '_ProcessProxy__initialized', True)
        logger.debug(f"进程代理对象已初始化: {obj_id}", color=Colors.DEBUG)

# Manager代理初始化方法已移除 - 使用纯RPC通信

    def __getattr__(self, name: str) -> Any:
        """
        属性获取拦截器 - 纯RPC实现

        Args:
            name (str): 属性名称

        Returns:
            Any: 属性值或方法代理

        使用示例：
            value = proxy.some_attribute  # 获取属性
            method = proxy.some_method  # 获取方法（返回可调用对象）
        """
        if not self.__initialized:
            raise AttributeError(f"代理对象未完全初始化")

        # 检查是否是特殊属性
        if name.startswith('_ProcessProxy__'):
            return object.__getattribute__(self, name)

        try:
            # 检查缓存
            cache_key = f"{self.__obj_id}.{name}"
            cached_value = self.__cache.get(cache_key)
            if cached_value is not None:
                logger.debug(f"缓存命中: {cache_key}", color=Colors.DEBUG)
                return cached_value

            # 通过RPC获取属性或方法
            return self.__create_rpc_method_proxy(name)

        except Exception as e:
            logger.error(f"获取属性失败: {self.__obj_id}.{name}, 错误: {e}", color=Colors.ERROR)
            raise AttributeError(f"'{self.__obj_id}' 对象没有属性 '{name}'")

    def __setattr__(self, name: str, value: Any):
        """
        属性设置拦截器 - 纯RPC实现

        Args:
            name (str): 属性名称
            value (Any): 属性值

        使用示例：
            proxy.some_attribute = new_value
        """
        # 处理内部属性
        if name.startswith('_ProcessProxy__') or not hasattr(self, '_ProcessProxy__initialized'):
            object.__setattr__(self, name, value)
            return

        if not self.__initialized:
            object.__setattr__(self, name, value)
            return

        try:
            # 通过RPC设置属性值
            command_processor = self.__rpc_manager._SimpleRPCManager__command_processor
            if command_processor is not None:
                # 使用特殊的属性设置命令
                command_processor.execute_command(self.__obj_id, f'__setattr__{name}', (value,), {})

                # 更新缓存
                cache_key = f"{self.__obj_id}.{name}"
                self.__cache.set(cache_key, value)

                logger.debug(f"属性已设置: {self.__obj_id}.{name} = {value}", color=Colors.DEBUG)
            else:
                raise RuntimeError("RPC管理器未启动")

        except Exception as e:
            logger.error(f"设置属性失败: {self.__obj_id}.{name}, 错误: {e}", color=Colors.ERROR)
            raise

    def __create_rpc_method_proxy(self, method_name: str) -> Callable:
        """
        创建RPC方法代理（私有方法）

        Args:
            method_name (str): 方法名称

        Returns:
            Callable: RPC代理方法
        """
        def rpc_method_proxy(*args, **kwargs):
            """RPC方法代理函数"""
            try:
                # 检查是否是子进程模式
                if self.__rpc_manager._SimpleRPCManager__is_child_process:
                    # 子进程模式：通过共享队列发送命令
                    return self.__execute_cross_process_command(method_name, args, kwargs)
                else:
                    # 主进程模式：直接通过CommandProcessor执行
                    command_processor = self.__rpc_manager._SimpleRPCManager__command_processor
                    if command_processor is None:
                        raise RuntimeError("RPC管理器未启动")

                    result = command_processor.execute_command(self.__obj_id, method_name, args, kwargs)

                    # 缓存逻辑
                    if (method_name.startswith('get_') or method_name.startswith('is_') or
                        method_name.startswith('has_')) and len(args) == 0 and len(kwargs) == 0:
                        cache_key = f"{self.__obj_id}.{method_name}"
                        self.__cache.set(cache_key, result)
                    elif method_name.startswith('set_'):
                        self.__cache.clear()

                    logger.debug(f"RPC方法调用成功: {self.__obj_id}.{method_name}", color=Colors.DEBUG)
                    return result

            except Exception as e:
                logger.error(f"RPC方法调用失败: {self.__obj_id}.{method_name}, 错误: {e}", color=Colors.ERROR)
                raise

        return rpc_method_proxy

    def __execute_cross_process_command(self, method_name: str, args: tuple, kwargs: dict):
        """
        执行跨进程命令（私有方法）

        Args:
            method_name (str): 方法名称
            args (tuple): 位置参数
            kwargs (dict): 关键字参数

        Returns:
            Any: 方法执行结果
        """
        try:
            # 获取共享队列（从RPC管理器中获取）
            command_queue = self.__rpc_manager._SimpleRPCManager__external_command_queue
            response_queue = self.__rpc_manager._SimpleRPCManager__external_response_queue

            if command_queue is None or response_queue is None:
                raise RuntimeError("共享队列未初始化")

            # 生成唯一的调用ID
            call_id = f"{os.getpid()}_{time.time()}_{uuid.uuid4().hex[:8]}"

            # 构建命令
            command = {
                'call_id': call_id,
                'obj_id': self.__obj_id,
                'method_name': method_name,
                'args': args,
                'kwargs': kwargs,
                'timestamp': time.time()
            }

            # 发送命令到主进程
            command_queue.put(command)
            logger.debug(f"跨进程命令已发送: {call_id}", color=Colors.DEBUG)

            # 等待响应
            timeout = 30.0  # 30秒超时
            start_time = time.time()

            while time.time() - start_time < timeout:
                try:
                    response = response_queue.get(timeout=1.0)
                    if response.get('call_id') == call_id:
                        # 找到对应的响应
                        if 'error' in response:
                            # 处理错误响应 - 增强的错误处理机制
                            error_info_str = response['error']

                            try:
                                # 尝试解析错误信息（支持字典和字符串格式）
                                if isinstance(error_info_str, dict):
                                    # 如果已经是字典格式，直接使用
                                    error_info = error_info_str
                                elif isinstance(error_info_str, str):
                                    # 如果是字符串格式，尝试解析
                                    import ast
                                    if error_info_str.startswith('{'):
                                        error_info = ast.literal_eval(error_info_str)
                                    else:
                                        # 如果是普通字符串，创建基本错误信息
                                        error_info = {
                                            'exception_type': 'RemoteError',
                                            'exception_message': str(error_info_str),
                                            'traceback': '',
                                            'process_id': 'Unknown',
                                            'context': {
                                                'obj_id': self.__obj_id,
                                                'method_name': method_name,
                                                'call_id': call_id
                                            }
                                        }
                                else:
                                    # 其他类型，创建基本错误信息
                                    error_info = {
                                        'exception_type': 'RemoteError',
                                        'exception_message': str(error_info_str),
                                        'traceback': '',
                                        'process_id': 'Unknown',
                                        'context': {
                                            'obj_id': self.__obj_id,
                                            'method_name': method_name,
                                            'call_id': call_id
                                        }
                                    }

                                # 创建并抛出增强的跨进程异常
                                enhanced_exception = CrossProcessException(error_info)
                                raise enhanced_exception

                            except CrossProcessException:
                                # 如果是CrossProcessException，直接重新抛出，不需要额外处理
                                raise
                            except (ValueError, SyntaxError, Exception) as parse_error:
                                # 如果解析失败，输出详细的错误信息并抛出基本异常
                                print(f"\n[子进程错误处理] 无法解析远程错误信息: {parse_error}")
                                print(f"[子进程错误处理] 原始错误信息: {error_info_str}")
                                print(f"[子进程错误处理] 调用上下文: obj_id={self.__obj_id}, method_name={method_name}")

                                # 使用traceback.print_exc()输出当前的错误堆栈
                                import traceback
                                print(f"[子进程错误处理] 当前堆栈信息:")
                                traceback.print_exc()

                                # 抛出包含详细信息的异常
                                raise Exception(f"远程执行错误 (obj_id={self.__obj_id}, method_name={method_name}): {error_info_str}")
                        else:
                            # 返回成功结果
                            result = response.get('result')
                            logger.debug(f"跨进程命令执行成功: {call_id}", color=Colors.DEBUG)
                            return result
                    else:
                        # 不是我们的响应，放回队列
                        response_queue.put(response)
                except queue.Empty:
                    continue

            raise TimeoutError(f"跨进程命令超时: {call_id}")

        except Exception as e:
            # 增强的错误处理：输出详细的错误信息
            print(f"\n[子进程异常] 跨进程命令执行失败:")
            print(f"  对象ID: {self.__obj_id}")
            print(f"  方法名: {method_name}")
            print(f"  参数: args={args}, kwargs={kwargs}")
            print(f"  错误类型: {type(e).__name__}")
            print(f"  错误消息: {e}")

            # 使用traceback.print_exc()输出详细的错误堆栈信息
            print(f"[子进程异常] 详细堆栈信息:")
            import traceback
            traceback.print_exc()

            logger.error(f"跨进程命令执行失败: {method_name}, 错误: {e}", color=Colors.ERROR)
            raise

# 旧的远程方法代理已移除 - 使用纯RPC通信

    def __call__(self, *args, **kwargs) -> Any:
        """
        支持可调用对象

        使用示例：
            result = proxy(*args, **kwargs)  # 如果代理对象本身是可调用的
        """
        return self.__create_rpc_method_proxy('__call__')(*args, **kwargs)

    def __getitem__(self, key) -> Any:
        """
        支持索引访问

        使用示例：
            value = proxy[key]
        """
        return self.__create_rpc_method_proxy('__getitem__')(key)

    def __setitem__(self, key, value):
        """
        支持索引设置

        使用示例：
            proxy[key] = value
        """
        self.__create_rpc_method_proxy('__setitem__')(key, value)

        # 清除缓存
        self.__cache.clear()

    def __len__(self) -> int:
        """
        支持len()函数

        使用示例：
            length = len(proxy)
        """
        return self.__create_rpc_method_proxy('__len__')()

    def __str__(self) -> str:
        """
        支持str()函数

        使用示例：
            text = str(proxy)
        """
        try:
            return self.__create_rpc_method_proxy('__str__')()
        except:
            return f"<ProcessProxy object '{self.__obj_id}'>"

    def __repr__(self) -> str:
        """
        支持repr()函数

        使用示例：
            representation = repr(proxy)
        """
        try:
            return self.__create_rpc_method_proxy('__repr__')()
        except:
            return f"<ProcessProxy(obj_id='{self.__obj_id}')>"

    def __bool__(self) -> bool:
        """
        支持bool()函数和条件判断

        使用示例：
            if proxy:  # 条件判断
                pass
        """
        try:
            return self.__create_rpc_method_proxy('__bool__')()
        except:
            return True  # 默认为True

    def _refresh_cache(self):
        """
        刷新缓存

        强制清除所有缓存，下次访问时重新获取数据。

        使用示例：
            proxy._refresh_cache()
        """
        self.__cache.clear()
        logger.debug(f"缓存已刷新: {self.__obj_id}", color=Colors.DEBUG)

    def _sync_with_main_process(self):
        """
        与主进程同步

        强制与主进程同步状态，确保获取最新数据。

        使用示例：
            proxy._sync_with_main_process()
        """
        # 刷新缓存
        self._refresh_cache()
        logger.debug(f"已与主进程同步: {self.__obj_id}", color=Colors.DEBUG)

    def _get_proxy_info(self) -> Dict[str, Any]:
        """
        获取代理对象信息

        Returns:
            Dict[str, Any]: 代理对象的详细信息

        使用示例：
            info = proxy._get_proxy_info()
            print(f"代理信息: {info}")
        """
        return {
            'obj_id': self.__obj_id,
            'rpc_manager_running': self.__rpc_manager._SimpleRPCManager__is_running,
            'cache_statistics': self.__cache.get_statistics(),
            'initialized': self.__initialized
        }


# ProxyFactory类已移除 - 使用简化的代理创建机制


# =====================
# 模块导出和便捷函数
# =====================

def create_rpc_manager(config=None) -> SimpleRPCManager:
    """
    便捷函数：创建简化RPC管理器

    Args:
        config (ProxyConfiguration, optional): 代理配置

    Returns:
        SimpleRPCManager: RPC管理器实例

    使用示例：
        # 使用默认配置
        manager = create_rpc_manager()
        manager.start()

        # 使用自定义配置
        config = ProxyConfiguration()
        config.optimize_for_scenario('high_frequency')
        manager = create_rpc_manager(config)
        manager.start()
    """
    return SimpleRPCManager(config=config)


def create_proxy(manager: SimpleRPCManager, obj_id: str) -> ProcessProxy:
    """
    便捷函数：创建代理对象

    Args:
        manager (SimpleRPCManager): RPC管理器
        obj_id (str): 对象ID

    Returns:
        ProcessProxy: 代理对象

    使用示例：
        proxy = create_proxy(manager, 'service')
        result = proxy.some_method()
    """
    return manager.create_proxy(obj_id)


# =====================
# 模块级别的导出
# =====================

__all__ = [
    # 核心类
    'SimpleRPCManager',
    'ProcessProxy',
    'BasicCache',

    # 配置和组件类
    'ProxyConfiguration',
    'ErrorHandler',
    'PerformanceMonitor',
    'CommandProcessor',

    # 便捷函数
    'create_rpc_manager',
    'create_proxy',
]


# =====================
# 模块使用示例和文档
# =====================

"""
ProcessProxy 简化RPC使用示例
===========================

1. 主进程设置：
--------------
```python
from global_tools.utils.enhanced_process_rpc.proxy_manager import SimpleRPCManager

# 定义服务类
class MyService:
    def __init__(self):
        self.data = {"count": 0}

    def increment(self):
        self.data["count"] += 1
        return self.data["count"]

    def get_data(self):
        return self.data

# 创建服务实例
service = MyService()

# 创建并启动RPC管理器
manager = SimpleRPCManager()
manager.start()

# 注册对象
manager.register_object('service', service)

print("RPC管理器已启动")
```

2. 子进程使用：
--------------
```python
import multiprocessing
from global_tools.utils.enhanced_process_rpc.proxy_manager import SimpleRPCManager, ProcessProxy

def child_worker():
    # 创建RPC管理器（子进程中）
    manager = SimpleRPCManager()

    # 创建代理对象
    service_proxy = ProcessProxy(manager, 'service')

    # 像使用本地对象一样使用代理
    result = service_proxy.increment()  # 调用方法
    print(f"子进程调用结果: {result}")

    data = service_proxy.get_data()  # 获取数据
    print(f"子进程获取数据: {data}")

# 启动子进程
process = multiprocessing.Process(target=child_worker)
process.start()
process.join()
```

3. 高级配置：
------------
```python
from global_tools.utils.enhanced_process_rpc.proxy_manager import ProxyConfiguration

# 创建自定义配置
config = ProxyConfiguration()
config.optimize_for_scenario('high_frequency')  # 优化为高频访问
config.debug_mode = True  # 启用调试模式

# 使用自定义配置创建管理器
manager = SimpleRPCManager(config=config)
```

4. 性能监控：
------------
```python
# 获取性能统计
stats = manager.get_statistics()
print(f"性能统计: {stats}")

# 获取缓存统计
proxy_info = service_proxy._get_proxy_info()
print(f"代理信息: {proxy_info}")
```

技术特性总结：
=============
- ✅ 纯Queue RPC通信机制
- ✅ 简洁的架构设计
- ✅ 基础缓存优化
- ✅ 完善的错误处理和调试支持
- ✅ 线程安全设计
- ✅ 详细的性能监控
- ✅ 易于维护和扩展

注意事项：
=========
1. 确保主进程在子进程之前启动RPC管理器
2. 在生产环境中关闭debug_mode以提高性能
3. 使用traceback.print_exc()获取详细错误信息
4. 定期清理缓存以避免内存泄漏

重要说明：
=========
当前实现为ProcessProxy系统的简化版本，专注于Queue RPC通信机制。
移除了复杂的Manager代理和混合分发逻辑，提供更简洁、易维护的架构。

架构优势：
- 纯RPC通信：统一的通信机制，易于理解和调试
- 简化设计：减少60%+代码量，降低维护成本
- 高可靠性：Queue机制提供稳定的跨进程通信
- 易扩展性：清晰的架构便于功能扩展
"""