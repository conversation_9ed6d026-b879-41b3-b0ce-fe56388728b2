#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跨进程RPC性能测试
================

本文件专门测试子进程调用主进程方法的性能，验证是否达到实时性要求。

测试项目：
1. 单次调用延迟测试
2. 批量调用性能测试
3. 不同数据大小的传输性能
4. 并发调用性能测试
5. 实时性要求验证

作者: AI Assistant
日期: 2025-01-14
"""

import time
import statistics
import multiprocessing
from typing import List, Dict, Any
from global_tools.utils.enhanced_process_rpc.proxy_manager import SimpleRPCManager


class PerformanceTestService:
    """性能测试服务类"""
    
    def __init__(self):
        self.call_count = 0
        self.start_time = time.time()
    
    def ping(self) -> str:
        """最简单的ping测试"""
        self.call_count += 1
        return "pong"
    
    def echo(self, message: str) -> str:
        """回声测试"""
        self.call_count += 1
        return f"Echo: {message}"
    
    def calculate(self, a: int, b: int) -> int:
        """简单计算测试"""
        self.call_count += 1
        return a + b
    
    def process_small_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理小数据量（<1KB）"""
        self.call_count += 1
        return {"processed": True, "input_size": len(str(data)), "timestamp": time.time()}
    
    def process_medium_data(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """处理中等数据量（1-10KB）"""
        self.call_count += 1
        return {
            "processed": True, 
            "item_count": len(data),
            "total_size": len(str(data)),
            "timestamp": time.time()
        }
    
    def process_large_data(self, data: List[str]) -> Dict[str, Any]:
        """处理大数据量（10-100KB）"""
        self.call_count += 1
        return {
            "processed": True,
            "item_count": len(data),
            "total_size": sum(len(item) for item in data),
            "timestamp": time.time()
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "call_count": self.call_count,
            "uptime": time.time() - self.start_time,
            "calls_per_second": self.call_count / (time.time() - self.start_time)
        }


def measure_latency(func, *args, **kwargs) -> float:
    """测量函数执行延迟（毫秒）"""
    start_time = time.perf_counter()
    result = func(*args, **kwargs)
    end_time = time.perf_counter()
    return (end_time - start_time) * 1000  # 转换为毫秒


def single_call_latency_test_simple(proxy) -> Dict[str, float]:
    """单次调用延迟测试"""
    print("\n" + "=" * 60)
    print("1. 单次调用延迟测试")
    print("=" * 60)
    
    latencies = {}

    # 测试get_message调用
    message_latencies = []
    for i in range(100):
        latency = measure_latency(proxy.get_message)
        message_latencies.append(latency)

    latencies['get_message'] = {
        'min': min(message_latencies),
        'max': max(message_latencies),
        'avg': statistics.mean(message_latencies),
        'median': statistics.median(message_latencies),
        'std': statistics.stdev(message_latencies) if len(message_latencies) > 1 else 0
    }

    # 测试get_counter调用
    counter_latencies = []
    for i in range(100):
        latency = measure_latency(proxy.get_counter)
        counter_latencies.append(latency)

    latencies['get_counter'] = {
        'min': min(counter_latencies),
        'max': max(counter_latencies),
        'avg': statistics.mean(counter_latencies),
        'median': statistics.median(counter_latencies),
        'std': statistics.stdev(counter_latencies) if len(counter_latencies) > 1 else 0
    }

    # 测试increment调用（有参数）
    increment_latencies = []
    for i in range(50):  # 减少次数避免计数器过大
        latency = measure_latency(proxy.increment)
        increment_latencies.append(latency)

    latencies['increment'] = {
        'min': min(increment_latencies),
        'max': max(increment_latencies),
        'avg': statistics.mean(increment_latencies),
        'median': statistics.median(increment_latencies),
        'std': statistics.stdev(increment_latencies) if len(increment_latencies) > 1 else 0
    }
    
    # 输出结果
    for method, stats in latencies.items():
        print(f"\n{method.upper()} 调用延迟统计 (100次调用):")
        print(f"  最小延迟: {stats['min']:.2f} ms")
        print(f"  最大延迟: {stats['max']:.2f} ms")
        print(f"  平均延迟: {stats['avg']:.2f} ms")
        print(f"  中位延迟: {stats['median']:.2f} ms")
        print(f"  标准差:   {stats['std']:.2f} ms")
        
        # 实时性评估
        if stats['avg'] < 1.0:
            print(f"  ✅ 实时性评级: 优秀 (< 1ms)")
        elif stats['avg'] < 5.0:
            print(f"  ✅ 实时性评级: 良好 (< 5ms)")
        elif stats['avg'] < 10.0:
            print(f"  ⚠️  实时性评级: 一般 (< 10ms)")
        else:
            print(f"  ❌ 实时性评级: 较差 (>= 10ms)")
    
    return latencies


def batch_performance_test_simple(proxy) -> Dict[str, Any]:
    """批量调用性能测试"""
    print("\n" + "=" * 60)
    print("2. 批量调用性能测试")
    print("=" * 60)
    
    batch_sizes = [10, 50, 100, 500, 1000]
    results = {}
    
    for batch_size in batch_sizes:
        print(f"\n测试批量大小: {batch_size}")
        
        # 测试批量get_message调用
        start_time = time.perf_counter()
        for i in range(batch_size):
            proxy.get_message()
        end_time = time.perf_counter()
        
        total_time = (end_time - start_time) * 1000  # 毫秒
        avg_latency = total_time / batch_size
        throughput = batch_size / (total_time / 1000)  # 调用/秒
        
        results[batch_size] = {
            'total_time': total_time,
            'avg_latency': avg_latency,
            'throughput': throughput
        }
        
        print(f"  总耗时: {total_time:.2f} ms")
        print(f"  平均延迟: {avg_latency:.2f} ms")
        print(f"  吞吐量: {throughput:.2f} 调用/秒")
        
        # 吞吐量评估
        if throughput > 1000:
            print(f"  ✅ 吞吐量评级: 优秀 (> 1000 调用/秒)")
        elif throughput > 500:
            print(f"  ✅ 吞吐量评级: 良好 (> 500 调用/秒)")
        elif throughput > 100:
            print(f"  ⚠️  吞吐量评级: 一般 (> 100 调用/秒)")
        else:
            print(f"  ❌ 吞吐量评级: 较差 (<= 100 调用/秒)")
    
    return results


def data_size_performance_test(proxy) -> Dict[str, Any]:
    """不同数据大小的传输性能测试"""
    print("\n" + "=" * 60)
    print("3. 数据传输性能测试")
    print("=" * 60)
    
    results = {}
    
    # 小数据测试 (<1KB)
    small_data = {"key": "value", "number": 123, "list": [1, 2, 3]}
    small_latencies = []
    for i in range(50):
        latency = measure_latency(proxy.process_small_data, small_data)
        small_latencies.append(latency)
    
    results['small'] = {
        'size': '< 1KB',
        'avg_latency': statistics.mean(small_latencies),
        'min_latency': min(small_latencies),
        'max_latency': max(small_latencies)
    }
    
    # 中等数据测试 (1-10KB)
    medium_data = [{"id": i, "name": f"item_{i}", "data": "x" * 100} for i in range(50)]
    medium_latencies = []
    for i in range(20):
        latency = measure_latency(proxy.process_medium_data, medium_data)
        medium_latencies.append(latency)
    
    results['medium'] = {
        'size': '1-10KB',
        'avg_latency': statistics.mean(medium_latencies),
        'min_latency': min(medium_latencies),
        'max_latency': max(medium_latencies)
    }
    
    # 大数据测试 (10-100KB)
    large_data = ["x" * 1000 for _ in range(50)]  # 约50KB
    large_latencies = []
    for i in range(10):
        latency = measure_latency(proxy.process_large_data, large_data)
        large_latencies.append(latency)
    
    results['large'] = {
        'size': '10-100KB',
        'avg_latency': statistics.mean(large_latencies),
        'min_latency': min(large_latencies),
        'max_latency': max(large_latencies)
    }
    
    # 输出结果
    for data_type, stats in results.items():
        print(f"\n{data_type.upper()} 数据传输 ({stats['size']}):")
        print(f"  平均延迟: {stats['avg_latency']:.2f} ms")
        print(f"  最小延迟: {stats['min_latency']:.2f} ms")
        print(f"  最大延迟: {stats['max_latency']:.2f} ms")
        
        # 数据传输性能评估
        if stats['avg_latency'] < 2.0:
            print(f"  ✅ 传输性能: 优秀 (< 2ms)")
        elif stats['avg_latency'] < 10.0:
            print(f"  ✅ 传输性能: 良好 (< 10ms)")
        elif stats['avg_latency'] < 50.0:
            print(f"  ⚠️  传输性能: 一般 (< 50ms)")
        else:
            print(f"  ❌ 传输性能: 较差 (>= 50ms)")
    
    return results


def realtime_requirement_test_simple(proxy) -> Dict[str, bool]:
    """实时性要求验证测试"""
    print("\n" + "=" * 60)
    print("4. 实时性要求验证")
    print("=" * 60)
    
    requirements = {
        "ultra_low_latency": 1.0,    # 超低延迟: < 1ms
        "low_latency": 5.0,          # 低延迟: < 5ms
        "interactive": 10.0,         # 交互式: < 10ms
        "responsive": 50.0,          # 响应式: < 50ms
        "acceptable": 100.0          # 可接受: < 100ms
    }
    
    # 进行1000次get_message测试
    latencies = []
    for i in range(1000):
        latency = measure_latency(proxy.get_message)
        latencies.append(latency)
    
    avg_latency = statistics.mean(latencies)
    p95_latency = sorted(latencies)[int(0.95 * len(latencies))]
    p99_latency = sorted(latencies)[int(0.99 * len(latencies))]
    
    print(f"\n实时性测试结果 (1000次get_message调用):")
    print(f"  平均延迟: {avg_latency:.2f} ms")
    print(f"  95%延迟: {p95_latency:.2f} ms")
    print(f"  99%延迟: {p99_latency:.2f} ms")
    
    results = {}
    print(f"\n实时性要求验证:")
    for req_name, threshold in requirements.items():
        meets_avg = avg_latency < threshold
        meets_p95 = p95_latency < threshold
        results[req_name] = meets_avg and meets_p95
        
        status = "✅" if results[req_name] else "❌"
        print(f"  {status} {req_name.replace('_', ' ').title()}: < {threshold}ms "
              f"(平均: {meets_avg}, 95%: {meets_p95})")
    
    return results


def child_process_performance_test():
    """子进程性能测试函数"""
    try:
        print("\n" + "=" * 80)
        print("子进程RPC性能测试开始")
        print("=" * 80)

        # 启动RPC管理器
        manager = SimpleRPCManager()
        manager.start()

        # 创建代理对象（使用已注册的服务）
        proxy = manager.create_proxy('test_service')
        
        # 执行各项性能测试（使用现有的测试服务方法）
        latency_results = single_call_latency_test_simple(proxy)
        batch_results = batch_performance_test_simple(proxy)
        realtime_results = realtime_requirement_test_simple(proxy)

        # 获取服务统计信息（使用现有方法）
        stats = {"message": proxy.get_message(), "counter": proxy.get_counter()}
        
        print("\n" + "=" * 60)
        print("5. 服务统计信息")
        print("=" * 60)
        print(f"当前消息: {stats['message']}")
        print(f"当前计数器: {stats['counter']}")
        print(f"服务状态: 正常运行")
        
        # 综合性能评估
        print("\n" + "=" * 60)
        print("6. 综合性能评估")
        print("=" * 60)
        
        avg_message_latency = latency_results['get_message']['avg']
        best_throughput = max(batch_results[size]['throughput'] for size in batch_results)
        
        print(f"平均get_message延迟: {avg_message_latency:.2f} ms")
        print(f"最佳吞吐量: {best_throughput:.2f} 调用/秒")
        
        # 实时性总结
        realtime_score = sum(realtime_results.values())
        total_requirements = len(realtime_results)
        
        print(f"\n实时性要求满足情况: {realtime_score}/{total_requirements}")
        if realtime_score >= 4:
            print("🏆 实时性评级: 优秀 - 满足大部分实时性要求")
        elif realtime_score >= 3:
            print("✅ 实时性评级: 良好 - 满足基本实时性要求")
        elif realtime_score >= 2:
            print("⚠️  实时性评级: 一般 - 部分满足实时性要求")
        else:
            print("❌ 实时性评级: 较差 - 不满足实时性要求")
        
        manager.stop()
        return True
        
    except Exception as e:
        print(f"性能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("跨进程RPC性能测试")
    print("=" * 80)

    # 导入现有的测试服务
    from cross_process_rpc_test import TestService

    # 创建RPC管理器
    manager = SimpleRPCManager()
    manager.start()

    # 创建并注册测试服务
    service = TestService()
    manager.register_object('test_service', service)

    print("性能测试服务已启动...")

    # 启动子进程进行性能测试
    process = multiprocessing.Process(target=child_process_performance_test)
    process.start()
    process.join()

    print(f"\n子进程性能测试完成，退出码: {process.exitcode}")

    # 关闭管理器
    manager.stop()
    print("性能测试完成")


if __name__ == "__main__":
    main()
