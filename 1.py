import sys
import os
import time
import copy

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import multiprocessing
# 直接导入模块
import global_tools.utils.enhanced_process_rpc.proxy_manager as proxy_module
from global_tools.utils.enhanced_process_rpc.proxy_manager import (
    SimpleRPCManager,
    ProcessProxy,
    ProxyConfiguration
)

class TestService:
    """
    测试服务类 - 用于验证RPC功能
    
    该类包含各种类型的方法和属性，用于全面测试RPC通信机制。
    """
    
    def __init__(self):
        """初始化测试服务"""
        self.counter = 0
        self.data = {"status": "初始状态", "operations": []}
        self.message = "Hello from main process"
        
    def increment(self):
        """递增计数器"""
        self.counter += 1
        print(f"[主进程] 计数器递增到: {self.counter}")
       
    
def child_worker_process():
    """
    子进程工作函数
    
    在子进程中创建代理对象并调用主进程的方法，修改主进程的状态。
    """
    print("\n" + "="*50)
    print("[子进程] 子进程开始执行")
    print("="*50)
    
    # 创建RPC管理器（子进程中不需要启动）
    manager = SimpleRPCManager()
    
    # 创建代理对象
    service_proxy = ProcessProxy(manager, 'test_service')
    print("[子进程] 代理对象已创建")
    service_proxy.increment()
        


def main():
    # 创建测试服务实例
    test_service = TestService()
    
    # 创建并启动RPC管理器
    config = ProxyConfiguration()
    config.debug_mode = True  # 启用调试模式
    manager = SimpleRPCManager(config)
    manager.start()
    # 注册测试服务
    manager.register_object('test_service', test_service)
    print("[测试] 测试服务已注册")
    
    # 创建代理对象
    service_proxy = manager.create_proxy('test_service')
    print("[测试] 代理对象已创建")
    # 启动子进程
    print("\n[主进程] 启动子进程...")
    process = multiprocessing.Process(target=child_worker_process)
    process.start()
    
    # 等待子进程完成
    process.join()
    print(f"\n[主进程] 子进程已完成，退出码: {process.exitcode}")

if __name__ == "__main__":
    main()
