# -*- coding: utf-8 -*-
"""
RPC功能测试文件
==============

测试重构后的ProcessProxy系统的核心功能：
1. 子进程调用主进程的方法
2. 子进程修改主进程的全局变量状态
3. 验证状态修改的正确性

作者：Augment Agent
版本：1.0.0
"""

import multiprocessing
import time
import sys
import os

# 添加项目路径到sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 清理可能存在的Logger实例冲突
try:
    from global_tools.utils import ClassInstanceManager
    # 尝试删除可能存在的Logger实例
    try:
        ClassInstanceManager.delete_instance("EnhancedProcessLogger")
    except:
        pass
    try:
        ClassInstanceManager.delete_instance("ProcessProxyLogger")
    except:
        pass
except:
    pass

from global_tools.utils.enhanced_process_rpc.proxy_manager import (
    SimpleRPCManager,
    ProcessProxy,
    ProxyConfiguration
)


class TestService:
    """
    测试服务类 - 用于验证RPC功能
    
    该类包含各种类型的方法和属性，用于全面测试RPC通信机制。
    """
    
    def __init__(self):
        """初始化测试服务"""
        self.counter = 0
        self.data = {"status": "初始状态", "operations": []}
        self.message = "Hello from main process"
        
    def increment(self):
        """递增计数器"""
        self.counter += 1
        self.data["operations"].append(f"increment -> {self.counter}")
        print(f"[主进程] 计数器递增到: {self.counter}")
        return self.counter
        
    def decrement(self):
        """递减计数器"""
        self.counter -= 1
        self.data["operations"].append(f"decrement -> {self.counter}")
        print(f"[主进程] 计数器递减到: {self.counter}")
        return self.counter
        
    def set_message(self, new_message: str):
        """设置消息"""
        old_message = self.message
        self.message = new_message
        self.data["operations"].append(f"set_message: '{old_message}' -> '{new_message}'")
        print(f"[主进程] 消息已更新: {new_message}")
        return old_message
        
    def get_counter(self):
        """获取计数器值"""
        print(f"[主进程] 获取计数器值: {self.counter}")
        return self.counter
        
    def get_message(self):
        """获取消息"""
        print(f"[主进程] 获取消息: {self.message}")
        return self.message
        
    def get_data(self):
        """获取完整数据"""
        print(f"[主进程] 获取完整数据: {self.data}")
        return self.data.copy()
        
    def update_status(self, status: str):
        """更新状态"""
        old_status = self.data["status"]
        self.data["status"] = status
        self.data["operations"].append(f"update_status: '{old_status}' -> '{status}'")
        print(f"[主进程] 状态已更新: {status}")
        
    def reset(self):
        """重置所有状态"""
        self.counter = 0
        self.data = {"status": "已重置", "operations": ["reset"]}
        self.message = "Reset message"
        print("[主进程] 所有状态已重置")
        
    def complex_operation(self, multiplier: int, prefix: str):
        """复杂操作 - 测试多参数方法"""
        result = self.counter * multiplier
        new_message = f"{prefix}: {result}"
        self.message = new_message
        self.data["operations"].append(f"complex_operation({multiplier}, '{prefix}') -> {result}")
        print(f"[主进程] 复杂操作完成: {new_message}")
        return result


def child_worker_process():
    """
    子进程工作函数
    
    在子进程中创建代理对象并调用主进程的方法，修改主进程的状态。
    """
    print("\n" + "="*50)
    print("[子进程] 子进程开始执行")
    print("="*50)
    
    try:
        # 创建RPC管理器（子进程中不需要启动）
        manager = SimpleRPCManager()
        
        # 创建代理对象
        service_proxy = ProcessProxy(manager, 'test_service')
        print("[子进程] 代理对象已创建")
        
        # 测试1: 获取初始状态
        print("\n[子进程] 测试1: 获取初始状态")
        initial_counter = service_proxy.get_counter()
        initial_message = service_proxy.get_message()
        initial_data = service_proxy.get_data()
        print(f"[子进程] 初始计数器: {initial_counter}")
        print(f"[子进程] 初始消息: {initial_message}")
        print(f"[子进程] 初始数据: {initial_data}")
        
        # 测试2: 修改计数器
        print("\n[子进程] 测试2: 修改计数器")
        for i in range(3):
            result = service_proxy.increment()
            print(f"[子进程] 递增结果: {result}")
            
        result = service_proxy.decrement()
        print(f"[子进程] 递减结果: {result}")
        
        # 测试3: 修改消息
        print("\n[子进程] 测试3: 修改消息")
        old_msg = service_proxy.set_message("Hello from child process!")
        print(f"[子进程] 消息修改成功，旧消息: {old_msg}")
        
        # 测试4: 更新状态
        print("\n[子进程] 测试4: 更新状态")
        service_proxy.update_status("子进程已修改")
        
        # 测试5: 复杂操作
        print("\n[子进程] 测试5: 复杂操作")
        complex_result = service_proxy.complex_operation(10, "子进程计算")
        print(f"[子进程] 复杂操作结果: {complex_result}")
        
        # 测试6: 获取最终状态
        print("\n[子进程] 测试6: 获取最终状态")
        final_counter = service_proxy.get_counter()
        final_message = service_proxy.get_message()
        final_data = service_proxy.get_data()
        print(f"[子进程] 最终计数器: {final_counter}")
        print(f"[子进程] 最终消息: {final_message}")
        print(f"[子进程] 最终数据: {final_data}")
        
        print("\n[子进程] 所有测试完成")
        
    except Exception as e:
        print(f"[子进程] 发生错误: {e}")
        import traceback
        traceback.print_exc()


def main():
    """
    主函数 - 测试RPC功能的完整流程
    """
    print("ProcessProxy RPC功能测试")
    print("="*60)
    
    # 创建测试服务实例
    test_service = TestService()
    print(f"[主进程] 测试服务已创建，初始状态:")
    print(f"  计数器: {test_service.counter}")
    print(f"  消息: {test_service.message}")
    print(f"  数据: {test_service.data}")
    
    # 创建并启动RPC管理器
    config = ProxyConfiguration()
    config.debug_mode = True  # 启用调试模式
    manager = SimpleRPCManager(config)
    manager.start()
    print("[主进程] RPC管理器已启动")
    
    # 注册测试服务
    manager.register_object('test_service', test_service)
    print("[主进程] 测试服务已注册")
    
    # 记录初始状态
    initial_state = {
        'counter': test_service.counter,
        'message': test_service.message,
        'data': test_service.data.copy()
    }
    
    # 启动子进程
    print("\n[主进程] 启动子进程...")
    process = multiprocessing.Process(target=child_worker_process)
    process.start()
    
    # 等待子进程完成
    process.join()
    print(f"\n[主进程] 子进程已完成，退出码: {process.exitcode}")
    
    # 验证状态变化
    print("\n" + "="*60)
    print("状态验证结果")
    print("="*60)
    
    print(f"初始状态:")
    print(f"  计数器: {initial_state['counter']}")
    print(f"  消息: {initial_state['message']}")
    print(f"  数据状态: {initial_state['data']['status']}")
    print(f"  操作历史: {initial_state['data']['operations']}")
    
    print(f"\n最终状态:")
    print(f"  计数器: {test_service.counter}")
    print(f"  消息: {test_service.message}")
    print(f"  数据状态: {test_service.data['status']}")
    print(f"  操作历史: {test_service.data['operations']}")
    
    # 验证预期变化
    print(f"\n验证结果:")
    counter_changed = test_service.counter != initial_state['counter']
    message_changed = test_service.message != initial_state['message']
    status_changed = test_service.data['status'] != initial_state['data']['status']
    operations_added = len(test_service.data['operations']) > len(initial_state['data']['operations'])
    
    print(f"  ✅ 计数器已修改: {counter_changed} (期望: True)")
    print(f"  ✅ 消息已修改: {message_changed} (期望: True)")
    print(f"  ✅ 状态已修改: {status_changed} (期望: True)")
    print(f"  ✅ 操作历史已更新: {operations_added} (期望: True)")
    
    # 总体验证
    all_tests_passed = all([counter_changed, message_changed, status_changed, operations_added])
    print(f"\n🎉 总体测试结果: {'通过' if all_tests_passed else '失败'}")
    
    if all_tests_passed:
        print("✅ 所有RPC功能测试通过！子进程成功调用主进程方法并修改了全局变量状态。")
    else:
        print("❌ 部分测试失败，请检查RPC通信机制。")
    
    # 关闭RPC管理器
    manager.shutdown()
    print("\n[主进程] RPC管理器已关闭")
    
    return 0 if all_tests_passed else 1


if __name__ == "__main__":
    # 设置multiprocessing启动方法（Windows兼容性）
    multiprocessing.set_start_method('spawn', force=True)
    
    exit_code = main()
    sys.exit(exit_code)
