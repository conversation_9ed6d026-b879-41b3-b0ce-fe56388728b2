#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超时参数使用示例
================

本文件演示如何在跨进程RPC调用中手动设置超时参数。

作者: AI Assistant
日期: 2025-01-14
"""

import time
from global_tools.utils.enhanced_process_rpc.proxy_manager import SimpleRPCManager


class ExampleService:
    """示例服务类"""
    
    def __init__(self):
        self.counter = 0
    
    def quick_method(self, value: int) -> int:
        """快速方法（1秒内完成）"""
        print(f"[主进程] quick_method开始，参数: {value}")
        time.sleep(0.5)  # 0.5秒操作
        result = value * 2
        print(f"[主进程] quick_method完成，结果: {result}")
        return result
    
    def medium_method(self, value: int) -> int:
        """中等耗时方法（3秒完成）"""
        print(f"[主进程] medium_method开始，参数: {value}")
        time.sleep(3.0)  # 3秒操作
        result = value * 3
        print(f"[主进程] medium_method完成，结果: {result}")
        return result
    
    def slow_method(self, value: int) -> int:
        """慢速方法（10秒完成）"""
        print(f"[主进程] slow_method开始，参数: {value}")
        time.sleep(10.0)  # 10秒操作
        result = value * 10
        print(f"[主进程] slow_method完成，结果: {result}")
        return result


def demonstrate_timeout_usage():
    """演示超时参数的使用方法"""
    
    # 创建RPC管理器
    manager = SimpleRPCManager()
    manager.start()
    
    # 注册服务
    service = ExampleService()
    manager.register_object('example_service', service)
    
    # 创建代理对象
    proxy = manager.create_proxy('example_service')
    
    print("=" * 60)
    print("超时参数使用示例")
    print("=" * 60)
    
    # 示例1：无限等待（默认行为）
    print("\n1. 无限等待模式（默认）")
    print("-" * 30)
    try:
        result = proxy.quick_method(5)
        print(f"✅ 无限等待调用成功，结果: {result}")
    except Exception as e:
        print(f"❌ 无限等待调用失败: {e}")
    
    # 示例2：设置充足的超时时间
    print("\n2. 设置充足的超时时间（5秒超时，方法需要3秒）")
    print("-" * 50)
    try:
        result = proxy.medium_method(7, _rpc_timeout=5.0)
        print(f"✅ 5秒超时调用成功，结果: {result}")
    except Exception as e:
        print(f"❌ 5秒超时调用失败: {e}")
    
    # 示例3：设置不足的超时时间
    print("\n3. 设置不足的超时时间（2秒超时，方法需要3秒）")
    print("-" * 50)
    try:
        result = proxy.medium_method(9, _rpc_timeout=2.0)
        print(f"✅ 2秒超时调用意外成功，结果: {result}")
    except Exception as e:
        print(f"❌ 2秒超时调用预期失败: {type(e).__name__}: {e}")
    
    # 示例4：不同超时时间的对比
    print("\n4. 不同超时时间的对比")
    print("-" * 30)
    
    # 4.1 短超时
    print("4.1 短超时（1秒）调用慢速方法（10秒）")
    try:
        result = proxy.slow_method(3, _rpc_timeout=1.0)
        print(f"✅ 1秒超时意外成功: {result}")
    except Exception as e:
        print(f"❌ 1秒超时预期失败: {type(e).__name__}")
    
    # 4.2 中等超时
    print("4.2 中等超时（5秒）调用慢速方法（10秒）")
    try:
        result = proxy.slow_method(4, _rpc_timeout=5.0)
        print(f"✅ 5秒超时意外成功: {result}")
    except Exception as e:
        print(f"❌ 5秒超时预期失败: {type(e).__name__}")
    
    # 4.3 充足超时
    print("4.3 充足超时（15秒）调用慢速方法（10秒）")
    try:
        result = proxy.slow_method(5, _rpc_timeout=15.0)
        print(f"✅ 15秒超时调用成功: {result}")
    except Exception as e:
        print(f"❌ 15秒超时调用失败: {e}")
    
    print("\n" + "=" * 60)
    print("超时参数使用示例完成")
    print("=" * 60)
    
    # 关闭管理器
    manager.stop()


def child_process_timeout_examples():
    """子进程中的超时参数使用示例"""
    
    print("\n" + "=" * 60)
    print("子进程中的超时参数使用")
    print("=" * 60)
    
    # 在子进程中，使用方法完全相同
    manager = SimpleRPCManager()
    manager.start()
    
    # 创建代理对象
    proxy = manager.create_proxy('example_service')
    
    # 各种超时设置的使用方法
    timeout_examples = [
        ("无限等待", None),
        ("1秒超时", 1.0),
        ("5秒超时", 5.0),
        ("10秒超时", 10.0),
        ("30秒超时", 30.0),
        ("1分钟超时", 60.0),
    ]
    
    for desc, timeout_value in timeout_examples:
        print(f"\n{desc}的使用方法:")
        if timeout_value is None:
            print(f"  result = proxy.method_name(param1, param2)")
        else:
            print(f"  result = proxy.method_name(param1, param2, _rpc_timeout={timeout_value})")
    
    manager.stop()


if __name__ == "__main__":
    # 运行演示
    demonstrate_timeout_usage()
    child_process_timeout_examples()
