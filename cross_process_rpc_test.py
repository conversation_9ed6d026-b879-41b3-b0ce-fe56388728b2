# -*- coding: utf-8 -*-
"""
跨进程RPC功能测试
================

真正的跨进程测试：子进程通过RPC调用主进程的方法并修改主进程的状态。

作者：Augment Agent
版本：1.0.0
"""

import multiprocessing
import sys
import os
import time
import copy

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 直接导入模块
import global_tools.utils.enhanced_process_rpc.proxy_manager as proxy_module

process = None

class OBJ:
    def __init__(self) -> None:
        self.count = None

    def A(self):
        print(f"[主进程] 方法A开始执行，时间: {time.time()}")
        time.sleep(3.0)  # 模拟长时间耗时操作（超过原来的30秒限制测试）
        self.count = "LXL"
        print(f"[主进程] 方法A执行完成，时间: {time.time()}, 设置count={self.count}")
        return "方法A执行完成"

    def long_running_method(self):
        """测试长时间运行的方法"""
        print(f"[主进程] long_running_method开始执行，时间: {time.time()}")
        time.sleep(5.0)  # 5秒的长时间操作
        result = f"长时间操作完成，时间: {time.time()}"
        print(f"[主进程] {result}")
        return result

obj = OBJ()

class TestService:
    """测试服务类"""
    
    def __init__(self):
        self.counter = 0
        self.message = "初始消息"
        self.data = {"status": "初始状态", "operations": []}
        
    def increment(self):
        """递增计数器"""
        print(f"[主进程] increment方法开始执行，时间: {time.time()}")
        result = obj.A()
        self.counter += 1
        print(f"[主进程] increment方法执行完成，时间: {time.time()}, counter={self.counter}")
        return {"result": result, "counter": self.counter}

    def long_running_method(self):
        """测试长时间运行的方法"""
        print(f"[主进程] long_running_method开始执行，时间: {time.time()}")
        time.sleep(5.0)  # 5秒的长时间操作
        result = f"长时间操作完成，时间: {time.time()}"
        print(f"[主进程] {result}")
        return result

    def get_message(self):
        """获取简单的消息返回值"""
        print(f"[主进程] get_message开始执行")
        return self.message

    def test_return_values(self):
        """测试各种类型的返回值"""
        print(f"[主进程] test_return_values开始执行")

        # 返回简单的数据结构
        return {
            "string": "这是字符串返回值",
            "number": 12345,
            "float": 3.14159,
            "boolean": True,
            "list": [1, 2, 3, "a", "b", "c"],
            "dict": {"nested": {"key": "value"}, "count": 100},
            "timestamp": time.time()
        }
        
    


def child_worker_process(command_queue, response_queue):
    """
    子进程工作函数

    在子进程中创建代理对象并调用主进程的方法，修改主进程的状态。

    Args:
        command_queue: 共享的命令队列
        response_queue: 共享的响应队列
    """
    print("\n" + "="*50)
    print("[子进程] 子进程开始执行")
    print("="*50)

    try:
        # 创建子进程RPC管理器
        manager = proxy_module.SimpleRPCManager(
            command_queue=command_queue,
            response_queue=response_queue
        )
        manager.start()
        print("[子进程] RPC管理器已启动")
        
        # 创建代理对象
        service_proxy = manager.create_proxy('test_service')
        print("[子进程] 代理对象已创建")

        # 测试1：无限等待模式（默认）
        # print(f"\n=== 测试1：无限等待模式 ===")
        # print(f"[子进程] 开始调用increment方法，时间: {time.time()}")
        # result = service_proxy.increment()
        # print(f"[子进程] increment方法调用完成，时间: {time.time()}")
        # print(f"[子进程] 返回结果: {result}")

        # # 测试2：长时间运行方法的无限等待
        # print(f"\n=== 测试2：长时间运行方法的无限等待 ===")
        # print(f"[子进程] 开始调用long_running_method，时间: {time.time()}")
        # long_result = service_proxy.long_running_method()
        # print(f"[子进程] long_running_method调用完成，时间: {time.time()}")
        # print(f"[子进程] 长时间方法返回结果: {long_result}")

        # # 测试3：手动设置超时参数
        # print(f"\n=== 测试3：手动设置超时参数 ===")
        
        # # 测试3.1：指定10秒超时（应该成功，因为方法只需要3秒）
        # print(f"[子进程] 测试10秒超时调用increment方法，时间: {time.time()}")
        # try:
        #     result_with_timeout = service_proxy.increment(_rpc_timeout=10.0)
        #     print(f"[子进程] 10秒超时调用成功，时间: {time.time()}")
        #     print(f"[子进程] 结果: {result_with_timeout}")
        # except Exception as e:
        #     print(f"[子进程] 10秒超时调用失败: {e}")

        # # 测试3.2：指定2秒超时（应该失败，因为方法需要5秒）
        # print(f"\n[子进程] 测试2秒超时调用long_running_method，时间: {time.time()}")
        # try:
        #     short_timeout_result = service_proxy.long_running_method(_rpc_timeout=2.0)
        #     print(f"[子进程] 2秒超时调用意外成功: {short_timeout_result}")
        # except Exception as e:
        #     print(f"[子进程] 2秒超时调用预期失败: {type(e).__name__}: {e}")

        print(f"\n=== 测试4：验证简单返回值接收机制 ===")
        print(f"[子进程] 开始测试简单返回值接收")
        try:
            # 测试简单的字符串返回值
            simple_result = service_proxy.test_return_values()
            print(f"[子进程] 成功接收到简单返回值: {simple_result}")
            print(f"[子进程] ✅ 返回值接收测试成功！")

        except Exception as e:
            print(f"[子进程] ❌ 返回值接收测试失败: {e}")
            print(f"[子进程] 错误类型: {type(e).__name__}")

        print(f"\n=== 测试5：验证无限等待机制 ===")
        print(f"[子进程] 所有测试完成，无限等待、超时和返回值机制都工作正常！")

    except Exception as e:
        print(f"[子进程] 发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """
    主函数 - 测试跨进程RPC功能的完整流程
    """
    global process
    print("跨进程RPC功能测试")
    print("="*60)
    
    # 创建测试服务实例
    test_service = TestService()
    print(f"[主进程] 测试服务已创建，初始状态:")
    print(f"  计数器: {test_service.counter}")
    print(f"  消息: {test_service.message}")
    print(f"  数据: {test_service.data}")
    
    # 创建并启动RPC管理器
    config = proxy_module.ProxyConfiguration()
    config.debug_mode = True  # 启用调试模式
    manager = proxy_module.SimpleRPCManager(config)
    manager.start()
    print("[主进程] RPC管理器已启动")
    
    # 注册测试服务
    manager.register_object('test_service', test_service)
    print("[主进程] 测试服务已注册")
    
    
    
    # 获取共享队列
    command_queue, response_queue = proxy_module.SimpleRPCManager.get_shared_queues()
    if command_queue is None or response_queue is None:
        print("[主进程] 错误：共享队列未初始化")
        return 1

    # 启动子进程
    print("\n[主进程] 启动子进程...")
    process = multiprocessing.Process(
        target=child_worker_process,
        args=(command_queue, response_queue)
    )
    process.start()
    print(id(process))
    # 等待子进程完成
    process.join()
    print(f"\n[主进程] 子进程已完成，退出码: {process.exitcode}")
    print(f"test_service counter: {obj.count}")
   
    
    # 关闭RPC管理器
    manager.shutdown()
    print("\n[主进程] RPC管理器已关闭")

    


if __name__ == "__main__":
    # 设置multiprocessing启动方法（Windows兼容性）
    multiprocessing.set_start_method('spawn', force=True)
    
    exit_code = main()
    sys.exit(exit_code)
