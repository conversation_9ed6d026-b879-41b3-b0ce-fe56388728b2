# -*- coding: utf-8 -*-
"""
跨进程RPC功能测试
================

真正的跨进程测试：子进程通过RPC调用主进程的方法并修改主进程的状态。

作者：Augment Agent
版本：1.0.0
"""

import multiprocessing
import sys
import os
import time
import copy

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 直接导入模块
import global_tools.utils.enhanced_process_rpc.proxy_manager as proxy_module

process = None

class OBJ:
    def __init__(self) -> None:
        self.count = None

    def A(self):
        print(f"[主进程] 方法A开始执行，时间: {time.time()}")
        time.sleep(2.0)  # 模拟耗时操作
        self.count = "LXL"
        print(f"[主进程] 方法A执行完成，时间: {time.time()}, 设置count={self.count}")
        return "方法A执行完成"

obj = OBJ()

class TestService:
    """测试服务类"""
    
    def __init__(self):
        self.counter = 0
        self.message = "初始消息"
        self.data = {"status": "初始状态", "operations": []}
        
    def increment(self):
        """递增计数器"""
        print(f"[主进程] increment方法开始执行，时间: {time.time()}")
        result = obj.A()
        self.counter += 1
        print(f"[主进程] increment方法执行完成，时间: {time.time()}, counter={self.counter}")
        return {"result": result, "counter": self.counter}
        
    


def child_worker_process(command_queue, response_queue):
    """
    子进程工作函数

    在子进程中创建代理对象并调用主进程的方法，修改主进程的状态。

    Args:
        command_queue: 共享的命令队列
        response_queue: 共享的响应队列
    """
    print("\n" + "="*50)
    print("[子进程] 子进程开始执行")
    print("="*50)

    try:
        # 创建子进程RPC管理器
        manager = proxy_module.SimpleRPCManager(
            command_queue=command_queue,
            response_queue=response_queue
        )
        manager.start()
        print("[子进程] RPC管理器已启动")
        
        # 创建代理对象
        service_proxy = manager.create_proxy('test_service')
        print("[子进程] 代理对象已创建")

        # 测试同步/异步执行模式
        print(f"[子进程] 开始调用increment方法，时间: {time.time()}")
        result = service_proxy.increment()
        print(f"[子进程] increment方法调用完成，时间: {time.time()}")
        print(f"[子进程] 返回结果: {result}")

        # 验证主进程状态是否已更新
        print(f"[子进程] 验证主进程状态更新...")
        time.sleep(0.5)  # 短暂等待

    except Exception as e:
        print(f"[子进程] 发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """
    主函数 - 测试跨进程RPC功能的完整流程
    """
    global process
    print("跨进程RPC功能测试")
    print("="*60)
    
    # 创建测试服务实例
    test_service = TestService()
    print(f"[主进程] 测试服务已创建，初始状态:")
    print(f"  计数器: {test_service.counter}")
    print(f"  消息: {test_service.message}")
    print(f"  数据: {test_service.data}")
    
    # 创建并启动RPC管理器
    config = proxy_module.ProxyConfiguration()
    config.debug_mode = True  # 启用调试模式
    manager = proxy_module.SimpleRPCManager(config)
    manager.start()
    print("[主进程] RPC管理器已启动")
    
    # 注册测试服务
    manager.register_object('test_service', test_service)
    print("[主进程] 测试服务已注册")
    
    
    
    # 获取共享队列
    command_queue, response_queue = proxy_module.SimpleRPCManager.get_shared_queues()
    if command_queue is None or response_queue is None:
        print("[主进程] 错误：共享队列未初始化")
        return 1

    # 启动子进程
    print("\n[主进程] 启动子进程...")
    process = multiprocessing.Process(
        target=child_worker_process,
        args=(command_queue, response_queue)
    )
    process.start()
    print(id(process))
    # 等待子进程完成
    process.join()
    print(f"\n[主进程] 子进程已完成，退出码: {process.exitcode}")
    print(f"test_service counter: {obj.count}")
   
    
    # 关闭RPC管理器
    manager.shutdown()
    print("\n[主进程] RPC管理器已关闭")

    # 添加短暂延迟，确保所有资源都能正确清理
    time.sleep(0.5)


if __name__ == "__main__":
    # 设置multiprocessing启动方法（Windows兼容性）
    multiprocessing.set_start_method('spawn', force=True)
    
    exit_code = main()
    sys.exit(exit_code)
